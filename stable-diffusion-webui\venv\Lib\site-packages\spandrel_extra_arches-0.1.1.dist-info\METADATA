Metadata-Version: 2.1
Name: spandrel_extra_arches
Version: 0.1.1
Summary: Implements extra model architectures for spandrel
Author: chaiNNer team
License: MIT
Keywords: spandrel,pytorch architecture,pytorch arch,model arch,model architecture
Classifier: Development Status :: 2 - Pre-Alpha
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: spandrel >=0.3.0
Requires-Dist: torch
Requires-Dist: torchvision
Requires-Dist: numpy
Requires-Dist: einops
Requires-Dist: typing-extensions

# Spandrel extra architectures

[![PyPI package](https://img.shields.io/badge/pip%20install-spandrel_extra_arches-brightgreen)](https://pypi.org/project/spandrel_extra_arches/)
[![version number](https://img.shields.io/pypi/v/spandrel_extra_arches?color=green&label=version)](https://github.com/chaiNNer-org/spandrel/releases)
[![PyPi Downloads](https://img.shields.io/pypi/dw/spandrel_extra_arches)](https://pypi.org/project/spandrel_extra_arches/#files)
[![Python Version](https://img.shields.io/pypi/pyversions/spandrel)](https://pypi.org/project/spandrel/#files:~:text=Requires%3A%20Python%20%3C3.12%2C%20%3E%3D3.8)

This library implements various PyTorch architectures with restrictive licenses for [spandrel](https://github.com/chaiNNer-org/spandrel).

If you are working on a private project or non-commercial open-source project, you are free to use this library. If you are working on a commercial or closed-source project, you may need to review the licenses of the individual architectures before using this library.

## Installation

You need to install both `spandrel` and `spandrel_extra_arches`:

```shell
pip install spandrel spandrel_extra_arches
```

## Basic usage

```python
from spandrel import MAIN_REGISTRY, ModelLoader
from spandrel_extra_arches import EXTRA_REGISTRY

# add extra architectures before `ModelLoader` is used
MAIN_REGISTRY.add(*EXTRA_REGISTRY)

# load a model from disk
model = ModelLoader().load_from_file(r"path/to/model.pth")

... # use model
```

## Model Architecture Support

For a full list of all architectures implemented in this library, see [the architectures marked with "(+)" here](https://github.com/chaiNNer-org/spandrel#model-architecture-support).

## License

This library is licensed under the MIT license but contains the source code of architectures with non-commercial and copyleft licenses. You may need to review the licenses of the individual architectures before using this library.
