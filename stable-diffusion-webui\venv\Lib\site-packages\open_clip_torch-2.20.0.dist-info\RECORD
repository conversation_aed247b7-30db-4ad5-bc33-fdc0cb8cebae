open_clip/__init__.py,sha256=ZyxNJRW88i6cPhdIocTkuidsGvirGu7y-TRQB7NMsp0,1182
open_clip/__pycache__/__init__.cpython-311.pyc,,
open_clip/__pycache__/coca_model.cpython-311.pyc,,
open_clip/__pycache__/constants.cpython-311.pyc,,
open_clip/__pycache__/factory.cpython-311.pyc,,
open_clip/__pycache__/generation_utils.cpython-311.pyc,,
open_clip/__pycache__/hf_configs.cpython-311.pyc,,
open_clip/__pycache__/hf_model.cpython-311.pyc,,
open_clip/__pycache__/loss.cpython-311.pyc,,
open_clip/__pycache__/model.cpython-311.pyc,,
open_clip/__pycache__/modified_resnet.cpython-311.pyc,,
open_clip/__pycache__/openai.cpython-311.pyc,,
open_clip/__pycache__/pretrained.cpython-311.pyc,,
open_clip/__pycache__/push_to_hf_hub.cpython-311.pyc,,
open_clip/__pycache__/timm_model.cpython-311.pyc,,
open_clip/__pycache__/tokenizer.cpython-311.pyc,,
open_clip/__pycache__/transform.cpython-311.pyc,,
open_clip/__pycache__/transformer.cpython-311.pyc,,
open_clip/__pycache__/utils.cpython-311.pyc,,
open_clip/__pycache__/version.cpython-311.pyc,,
open_clip/__pycache__/zero_shot_classifier.cpython-311.pyc,,
open_clip/__pycache__/zero_shot_metadata.cpython-311.pyc,,
open_clip/bpe_simple_vocab_16e6.txt.gz,sha256=kkaRrCiOVECSNhFWUq1KolD0ggPeUKnkcipuzUjWgEo,1356917
open_clip/coca_model.py,sha256=Y3YtSb17Z1GNnJ-wqW9p470vfMU25RTZNyaDphMR0Hs,17439
open_clip/constants.py,sha256=Qon6wpiQji-4FP-EtFlP9JCUp-qwdP7RsspkKTOekjs,116
open_clip/factory.py,sha256=4yqphInakR9toAwKMHiBp6R44i9Z5zGRCxa9gmk81_Y,14349
open_clip/generation_utils.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
open_clip/hf_configs.py,sha256=0CTMXbVAcHGyIePQawmtzxH9Nf4r13kvTEXgrads1uA,2047
open_clip/hf_model.py,sha256=-JvDwAQ79i7yBtikiVnQ1EsWgyU4Kc2RBn557X_fOHc,6940
open_clip/loss.py,sha256=YlfuGsjlMXj0OTh_jmERui0Qs2KkdHXjsFEUBdJOfYI,7943
open_clip/model.py,sha256=2aDxHUwJa8JJ_DGPltuRAHfOXA9k8g2D_u3zmWzrCDM,18568
open_clip/model_configs/EVA01-g-14-plus.json,sha256=gYprvWYeJ6az_w1MJuU5RRbPQHsG_VxFsFP9Nguo_YA,401
open_clip/model_configs/EVA01-g-14.json,sha256=FYgy4kV7ClILBOs_byIGzDreki68_nRgtXzEm1CcRYo,400
open_clip/model_configs/EVA02-B-16.json,sha256=cQxG9f_j6FDRXgcP97YPUZd71bF4fSqugccqUkPLkfs,404
open_clip/model_configs/EVA02-E-14-plus.json,sha256=bl7Tr0zCY9tlNcjuv1_Ru_cN9wbA8FbiPONj7gsuxjA,411
open_clip/model_configs/EVA02-E-14.json,sha256=ZJC9wal4bCzThPtPlkJe00OHXC9Hkajtt6WaDO4GCx8,411
open_clip/model_configs/EVA02-L-14-336.json,sha256=_TkQOAbWMWpiDnCxU9Ga_f5tHJJdYMVG6PX__rAilh8,406
open_clip/model_configs/EVA02-L-14.json,sha256=JzxP7DlkbLDyX1-FzRM7AN3K6BffxpK3mRViF_2T0r4,406
open_clip/model_configs/RN101-quickgelu.json,sha256=JN1DXuxocddnW9awIJjnHhBalXqmo6WRU7AxgRA3VrU,388
open_clip/model_configs/RN101.json,sha256=BGaq5ywc0lmoZV4lrzFw1UrLTLrJUwTLmDLWjgcWU4s,364
open_clip/model_configs/RN50-quickgelu.json,sha256=B56cTz14CKdn3ggGGrQX-Y2eiqrs7GUQlJytDAw4mkE,389
open_clip/model_configs/RN50.json,sha256=pbmEWkS1oFL7FpcFW0G0khcaqEho8UCOX-1-YHfQRtQ,364
open_clip/model_configs/RN50x16.json,sha256=lxBkeH_cAma7Udkk229_UxF2MRhzMxW1TyqeUr22TVA,365
open_clip/model_configs/RN50x4.json,sha256=rqObTslSzP4bf5JCqcDTbhq-uQ9cb9Syz6xuQCRi33I,365
open_clip/model_configs/RN50x64.json,sha256=yVRyYq8bc5ymR2YkaxLZteUfM8NU8rsdj-hx27Le094,370
open_clip/model_configs/ViT-B-16-plus-240.json,sha256=CbKmXqe_dXRAuJtS56Cf-F1VZjXw46TKdhCeQqrG41E,295
open_clip/model_configs/ViT-B-16-plus.json,sha256=jgxkXJGQs0xIwoHLwkPh7Xw_08HuXyxYhf6cF7EcMno,295
open_clip/model_configs/ViT-B-16.json,sha256=ZtSHZ6sRWR9K8tLRvyGEXD8r6hescOCYmZH_FmpTrlI,294
open_clip/model_configs/ViT-B-32-plus-256.json,sha256=_LdxkUxZNx7CIvBd1a9Epdp0xyhVwcaG8_lwHJXWRzI,295
open_clip/model_configs/ViT-B-32-quickgelu.json,sha256=Q33GrMhFUmfCZG6E-ICgJtkawggo25sQCxil7boOdno,318
open_clip/model_configs/ViT-B-32.json,sha256=dD9PzuOuoXBcGGX4mjdn2BZRN2n1frSDqe6j5hDr5jM,294
open_clip/model_configs/ViT-H-14.json,sha256=i1vRHhd18P15InugXAfzM5scTaLg8h9UJmMXdKhKYFg,324
open_clip/model_configs/ViT-H-16.json,sha256=RclQ6MmDALXgCFeB6fkcxvEv-UtD0O0PloftmTIJ6O8,324
open_clip/model_configs/ViT-L-14-280.json,sha256=hI9C2yS-2lXOBE-4LMT_rjT6OnaRhMIYenHlB03yCcE,296
open_clip/model_configs/ViT-L-14-336.json,sha256=mA2NomvxG2UUJq3bETiB8k9wdYnr7Me4giaeNnizPoQ,296
open_clip/model_configs/ViT-L-14.json,sha256=YWdQqSfqjYsLjk7JMXnKs9-eTxUxy2DMXdaILLBjiIs,296
open_clip/model_configs/ViT-L-16-320.json,sha256=5Ldmh1qlsbMD24BLtEbUx4IT7TS8CJ55KXsUKHuVIzE,296
open_clip/model_configs/ViT-L-16.json,sha256=6uiTJ3E35s1PBxJlQj0TVmNSgBjME72_UHCf6qgChPo,296
open_clip/model_configs/ViT-M-16-alt.json,sha256=p4iNq4dGbSbN1_5rHfmVwXAoyiG45oSKqf9UnNV_3wg,325
open_clip/model_configs/ViT-M-16.json,sha256=IoUu5tHkyXf5m3nYN_4ibDFfdcFqIvgJoW32osF5LUk,294
open_clip/model_configs/ViT-M-32-alt.json,sha256=ob4sUqgT91KEJoPGBHJxK75s8QqK2-7P-Uz-DJ9e8xA,294
open_clip/model_configs/ViT-M-32.json,sha256=MLhmw8SIqEGQPwzgiSIkz2zYiSQYai8b9cypD-PlKe8,294
open_clip/model_configs/ViT-S-16-alt.json,sha256=N3_5asMSYSXC1Z9xVnVCq0VjIwsc-UKHYU3RLfhueP8,294
open_clip/model_configs/ViT-S-16.json,sha256=aKOyZ9otmNqlLW33-ed2Tf5othni3c2gq3oAdNUFe-s,294
open_clip/model_configs/ViT-S-32-alt.json,sha256=jmDaGagupC58lxAq5VHTFJfaZtuxF5OACHVYT8tGx2A,294
open_clip/model_configs/ViT-S-32.json,sha256=-O7vFHWaY6J2hqvX66Ra5Ftp8DRMMvv_Z8BZyPAW2js,294
open_clip/model_configs/ViT-bigG-14.json,sha256=b1_E6Fc5yjVGgE4wF49SQbO-K-zYz1LCgrbpDHQqnzA,354
open_clip/model_configs/ViT-e-14.json,sha256=J-jTKhx3J--E-0qywze2n8t6rpYkPIvPtLS7r_0qg9E,354
open_clip/model_configs/ViT-g-14.json,sha256=RjFgqTL3V3Te0kHUIX7r0XL-fAPCRkZTCiS4myAUjfU,353
open_clip/model_configs/coca_ViT-B-32.json,sha256=XcCpp4A_77RfrdeK5TSDnSrcJY7-S3Mv35w3C0nK7uU,659
open_clip/model_configs/coca_ViT-L-14.json,sha256=cJdoI5plmP4T8Z8cz_0VLVA8TFGEfqIR9K7-whWKkfg,664
open_clip/model_configs/coca_base.json,sha256=QeqIUpxScAcV66XpCsDk7cpEUzRhldHDBctxq32MdeA,669
open_clip/model_configs/coca_roberta-ViT-B-32.json,sha256=MXO94qCV5-Z9-G5fz2mM7lJmOyyvD82tkSgLnN-GPB4,517
open_clip/model_configs/convnext_base.json,sha256=rKdJVouqy-7U2egCWUQPN3AjtCdErCarLzoBoO7qm4Q,421
open_clip/model_configs/convnext_base_w.json,sha256=PeHvsFXlBsBZUu8T-u9u7avNaKAB0PffLFlVQfPD9mo,422
open_clip/model_configs/convnext_base_w_320.json,sha256=N7Oe8DbmyUmvHVovQE5GnMIW4u5z0pDQ4xaeMm82fpM,422
open_clip/model_configs/convnext_large.json,sha256=Vx54lAdgqfOJFp2p7aAPMvWEu5gUvFB_t55QUcdxwps,423
open_clip/model_configs/convnext_large_d.json,sha256=dqjWhzNft13ALBC6xd5m9_aH46uLI88PNy6_hvsM5Yo,420
open_clip/model_configs/convnext_large_d_320.json,sha256=RMjeo5GvgI044W1P9Ini9Ji7-jWpdN659yc9iTLFZnk,420
open_clip/model_configs/convnext_small.json,sha256=VMDYthaLankH4y_4E07wlqobHiEAyBXzZrcot_zt0SI,422
open_clip/model_configs/convnext_tiny.json,sha256=yRlymoGMSPlTcci1IDDJZRPPLTjiipSgmL542js0Xok,422
open_clip/model_configs/convnext_xlarge.json,sha256=Mg9u4ZTbl7O5hRHM2QhvKdZcppv-GIlobwtNP9pDNPI,426
open_clip/model_configs/convnext_xxlarge.json,sha256=UBfIV3v-l5688iew1lG3g4CXF6Ehd5BdfOcSVLNjEI4,427
open_clip/model_configs/convnext_xxlarge_320.json,sha256=q09Gn5743xYyWekfFF8Key_sIYJPscLfqGPMarAbUvQ,427
open_clip/model_configs/mt5-base-ViT-B-32.json,sha256=3qEED4_Fzgl6e6OEVOMIsZ0ALfb5ChdVbUju2UwZqWA,325
open_clip/model_configs/mt5-xl-ViT-H-14.json,sha256=NWtl4eMbDk7jdLPS8zJjGxMVW41i9ZelaJmSkuPj1uo,349
open_clip/model_configs/roberta-ViT-B-32.json,sha256=Sm1xQlLJcDfA9RlBWYgtGjmRRvNUgydVgazCfJlKvhE,343
open_clip/model_configs/swin_base_patch4_window7_224.json,sha256=HLgjufq5vYUdgawK91HMt_qe0euzGhbOop4Byow-3Yc,380
open_clip/model_configs/vit_medium_patch16_gap_256.json,sha256=UEJjl_xuKOIF31W5qt7cScR-c5BCKepYj87yng5loH8,377
open_clip/model_configs/vit_relpos_medium_patch16_cls_224.json,sha256=sFOa4Z2v-Y1O36v9cRqAjgG8Ni4jQUQ43RWOxDDdeyg,384
open_clip/model_configs/xlm-roberta-base-ViT-B-32.json,sha256=8sPUVCy0p3L-zr6qKapKmBwpvwM38IpQIQzG1KU3Jms,327
open_clip/model_configs/xlm-roberta-large-ViT-H-14.json,sha256=F_vd07VqRT8U7Z3_GipIq-NCX2BobTzwUxlAkh-Km74,357
open_clip/modified_resnet.py,sha256=iXU00lYR-4NSD3ScsZ4AC34ZvG0ZRgcsA-KNw3BX0TY,7035
open_clip/openai.py,sha256=qFdhZBq4Rd1_Z2tEEUDp_oRr448w5LtWtINUslhAwmA,3302
open_clip/pretrained.py,sha256=KD1eDs39TS-wEBvgBMBlWShV0qsj_4RDjdqQjQfsbI0,17737
open_clip/push_to_hf_hub.py,sha256=AIBXKoPX9bs40FeLReNvU83Y4DFricPJVZFUcMy68Lk,8995
open_clip/timm_model.py,sha256=FSJ67lTQb6bYueQXIvRd71p-HQ9nPtGHSGBe6GIRTmY,5753
open_clip/tokenizer.py,sha256=X1Xp4gUP6zFopPzviBuxPvFC83opjSiNPajL8H-DmhQ,7411
open_clip/transform.py,sha256=QcXP7gHH4-LynFo5NJVCmO0_87xz9FqJmEJyg4ccRLo,4779
open_clip/transformer.py,sha256=Y-AFoomOqIPs7Va14ss_KP9Gu6UNRQwPLdcfzFcKzUI,27232
open_clip/utils.py,sha256=8CZ7udVAKxtbOKkPlvbNkAKsIdLbDz8_vujtSLL0YEc,3472
open_clip/version.py,sha256=eOhpY3fgwEYGNSbdSadam6N-mlJ9dQj6PorvpV9xbmU,23
open_clip/zero_shot_classifier.py,sha256=tHz0YAScl1TM4SHEGhjgfoRHVts2Pfs71-QCpHOmDjM,4345
open_clip/zero_shot_metadata.py,sha256=O9itsWi9-A_WgacSWOZ_LE0Pjtc-rptQTtUGPhcmu-k,19245
open_clip_torch-2.20.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
open_clip_torch-2.20.0.dist-info/LICENSE,sha256=SltPE-pHkqghGmnzLStGC21SDlfNI8YwHnB8dqbpelU,1229
open_clip_torch-2.20.0.dist-info/METADATA,sha256=VU05ddNjscrt1FcBxI3nkO5JP1ZHb6QjxRKnD-64w5E,46775
open_clip_torch-2.20.0.dist-info/RECORD,,
open_clip_torch-2.20.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
open_clip_torch-2.20.0.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
open_clip_torch-2.20.0.dist-info/top_level.txt,sha256=PVW9iuN0zYassfaMQZjFuKAcBAburXASxvO5vP3gfYk,19
training/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
training/__pycache__/__init__.cpython-311.pyc,,
training/__pycache__/data.cpython-311.pyc,,
training/__pycache__/distributed.cpython-311.pyc,,
training/__pycache__/file_utils.cpython-311.pyc,,
training/__pycache__/logger.cpython-311.pyc,,
training/__pycache__/main.cpython-311.pyc,,
training/__pycache__/params.cpython-311.pyc,,
training/__pycache__/precision.cpython-311.pyc,,
training/__pycache__/profile.cpython-311.pyc,,
training/__pycache__/scheduler.cpython-311.pyc,,
training/__pycache__/train.cpython-311.pyc,,
training/__pycache__/zero_shot.cpython-311.pyc,,
training/data.py,sha256=HizQ-h98wO7Ek55jThLo9Fym7mShd0lgyzcEqlZ2Cp8,20392
training/distributed.py,sha256=e7g1j2f0OB-vwdOYYepu_KB2IbWw0nNuptU_oCCUHrM,4444
training/file_utils.py,sha256=N7nhZmBf7vqN5bjSsAMsyuhwi17fpU2CNT2vx4vxtKo,2686
training/logger.py,sha256=2Q_60KQunY8bFa9qDbgZ4XjWqID4IDxqUTaIVs0m8-4,899
training/main.py,sha256=e-k590BowSUrgfv6OHgHrKPV3XGSf9LGkInGPUn30r4,20251
training/params.py,sha256=q6vwGY2EwZLdSSlyXOnkr5ko72XB-NSj7gsfokR-mHQ,14831
training/precision.py,sha256=KcfRtcdJoi1N7JxzyLDtgoH7D_mDRY6pwnloXql9x2U,383
training/profile.py,sha256=eDEZM9LB2TU4fKk6d0xjASWXpYsT9VS075oAV0ruETY,5289
training/scheduler.py,sha256=U8SBCfiixO3aDkF7vkqSVyQoJtR_bt99S_FrQ4F4mgU,1747
training/train.py,sha256=l2UK552qIvJhrrospd3w_BVx4KzQkralsEmVTWATKKg,14295
training/zero_shot.py,sha256=dICuY42NlMffXHdy9ooTTxtOcL2rlx2K1tU3qwjSRC8,2844
