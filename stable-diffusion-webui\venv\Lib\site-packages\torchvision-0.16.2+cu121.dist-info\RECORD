torchvision-0.16.2+cu121.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.16.2+cu121.dist-info/LICENSE,,
torchvision-0.16.2+cu121.dist-info/METADATA,,
torchvision-0.16.2+cu121.dist-info/RECORD,,
torchvision-0.16.2+cu121.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torchvision-0.16.2+cu121.dist-info/WHEEL,,
torchvision-0.16.2+cu121.dist-info/top_level.txt,,
torchvision-0.16.2+cu121.dist-info\LICENSE,sha256=wGNj-dM2J9xRc7E1IkRMyF-7Rzn2PhbUWH1cChZbWx4,1546
torchvision-0.16.2+cu121.dist-info\METADATA,sha256=n71vgfdE4yytC5bXiFKzXIs7YgDJ-f90IwD_Bu8X8Qs,6583
torchvision-0.16.2+cu121.dist-info\RECORD,,
torchvision-0.16.2+cu121.dist-info\WHEEL,sha256=TxwUeV-3HEbjjXVQ7qnUZIKQ4IinhZYV_rmkwq84M_Y,102
torchvision-0.16.2+cu121.dist-info\top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision/_C.pyd,,
torchvision/__init__.py,,
torchvision/__pycache__/__init__.cpython-311.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-311.pyc,,
torchvision/__pycache__/_meta_registrations.cpython-311.pyc,,
torchvision/__pycache__/_utils.cpython-311.pyc,,
torchvision/__pycache__/extension.cpython-311.pyc,,
torchvision/__pycache__/utils.cpython-311.pyc,,
torchvision/__pycache__/version.cpython-311.pyc,,
torchvision/_internally_replaced_utils.py,,
torchvision/_meta_registrations.py,,
torchvision/_utils.py,,
torchvision/cudart64_12.dll,,
torchvision/datasets/__init__.py,,
torchvision/datasets/__pycache__/__init__.cpython-311.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-311.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-311.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-311.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-311.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-311.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-311.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-311.pyc,,
torchvision/datasets/__pycache__/coco.cpython-311.pyc,,
torchvision/datasets/__pycache__/country211.cpython-311.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-311.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-311.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-311.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-311.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-311.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-311.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-311.pyc,,
torchvision/datasets/__pycache__/folder.cpython-311.pyc,,
torchvision/datasets/__pycache__/food101.cpython-311.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-311.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-311.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-311.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-311.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-311.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-311.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-311.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-311.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-311.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-311.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-311.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-311.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-311.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-311.pyc,,
torchvision/datasets/__pycache__/places365.cpython-311.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-311.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-311.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-311.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-311.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-311.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-311.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-311.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-311.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-311.pyc,,
torchvision/datasets/__pycache__/usps.cpython-311.pyc,,
torchvision/datasets/__pycache__/utils.cpython-311.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-311.pyc,,
torchvision/datasets/__pycache__/vision.cpython-311.pyc,,
torchvision/datasets/__pycache__/voc.cpython-311.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-311.pyc,,
torchvision/datasets/_optical_flow.py,,
torchvision/datasets/_stereo_matching.py,,
torchvision/datasets/caltech.py,,
torchvision/datasets/celeba.py,,
torchvision/datasets/cifar.py,,
torchvision/datasets/cityscapes.py,,
torchvision/datasets/clevr.py,,
torchvision/datasets/coco.py,,
torchvision/datasets/country211.py,,
torchvision/datasets/dtd.py,,
torchvision/datasets/eurosat.py,,
torchvision/datasets/fakedata.py,,
torchvision/datasets/fer2013.py,,
torchvision/datasets/fgvc_aircraft.py,,
torchvision/datasets/flickr.py,,
torchvision/datasets/flowers102.py,,
torchvision/datasets/folder.py,,
torchvision/datasets/food101.py,,
torchvision/datasets/gtsrb.py,,
torchvision/datasets/hmdb51.py,,
torchvision/datasets/imagenet.py,,
torchvision/datasets/inaturalist.py,,
torchvision/datasets/kinetics.py,,
torchvision/datasets/kitti.py,,
torchvision/datasets/lfw.py,,
torchvision/datasets/lsun.py,,
torchvision/datasets/mnist.py,,
torchvision/datasets/moving_mnist.py,,
torchvision/datasets/omniglot.py,,
torchvision/datasets/oxford_iiit_pet.py,,
torchvision/datasets/pcam.py,,
torchvision/datasets/phototour.py,,
torchvision/datasets/places365.py,,
torchvision/datasets/rendered_sst2.py,,
torchvision/datasets/samplers/__init__.py,,
torchvision/datasets/samplers/__pycache__/__init__.cpython-311.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-311.pyc,,
torchvision/datasets/samplers/clip_sampler.py,,
torchvision/datasets/sbd.py,,
torchvision/datasets/sbu.py,,
torchvision/datasets/semeion.py,,
torchvision/datasets/stanford_cars.py,,
torchvision/datasets/stl10.py,,
torchvision/datasets/sun397.py,,
torchvision/datasets/svhn.py,,
torchvision/datasets/ucf101.py,,
torchvision/datasets/usps.py,,
torchvision/datasets/utils.py,,
torchvision/datasets/video_utils.py,,
torchvision/datasets/vision.py,,
torchvision/datasets/voc.py,,
torchvision/datasets/widerface.py,,
torchvision/extension.py,,
torchvision/image.pyd,,
torchvision/io/__init__.py,,
torchvision/io/__pycache__/__init__.cpython-311.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-311.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-311.pyc,,
torchvision/io/__pycache__/image.cpython-311.pyc,,
torchvision/io/__pycache__/video.cpython-311.pyc,,
torchvision/io/__pycache__/video_reader.cpython-311.pyc,,
torchvision/io/_load_gpu_decoder.py,,
torchvision/io/_video_opt.py,,
torchvision/io/image.py,,
torchvision/io/video.py,,
torchvision/io/video_reader.py,,
torchvision/jpeg8.dll,,
torchvision/libpng16.dll,,
torchvision/models/__init__.py,,
torchvision/models/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/__pycache__/_api.cpython-311.pyc,,
torchvision/models/__pycache__/_meta.cpython-311.pyc,,
torchvision/models/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/__pycache__/alexnet.cpython-311.pyc,,
torchvision/models/__pycache__/convnext.cpython-311.pyc,,
torchvision/models/__pycache__/densenet.cpython-311.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-311.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-311.pyc,,
torchvision/models/__pycache__/googlenet.cpython-311.pyc,,
torchvision/models/__pycache__/inception.cpython-311.pyc,,
torchvision/models/__pycache__/maxvit.cpython-311.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-311.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-311.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-311.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-311.pyc,,
torchvision/models/__pycache__/regnet.cpython-311.pyc,,
torchvision/models/__pycache__/resnet.cpython-311.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-311.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-311.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-311.pyc,,
torchvision/models/__pycache__/vgg.cpython-311.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-311.pyc,,
torchvision/models/_api.py,,
torchvision/models/_meta.py,,
torchvision/models/_utils.py,,
torchvision/models/alexnet.py,,
torchvision/models/convnext.py,,
torchvision/models/densenet.py,,
torchvision/models/detection/__init__.py,,
torchvision/models/detection/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-311.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-311.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-311.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-311.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-311.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-311.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-311.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-311.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-311.pyc,,
torchvision/models/detection/_utils.py,,
torchvision/models/detection/anchor_utils.py,,
torchvision/models/detection/backbone_utils.py,,
torchvision/models/detection/faster_rcnn.py,,
torchvision/models/detection/fcos.py,,
torchvision/models/detection/generalized_rcnn.py,,
torchvision/models/detection/image_list.py,,
torchvision/models/detection/keypoint_rcnn.py,,
torchvision/models/detection/mask_rcnn.py,,
torchvision/models/detection/retinanet.py,,
torchvision/models/detection/roi_heads.py,,
torchvision/models/detection/rpn.py,,
torchvision/models/detection/ssd.py,,
torchvision/models/detection/ssdlite.py,,
torchvision/models/detection/transform.py,,
torchvision/models/efficientnet.py,,
torchvision/models/feature_extraction.py,,
torchvision/models/googlenet.py,,
torchvision/models/inception.py,,
torchvision/models/maxvit.py,,
torchvision/models/mnasnet.py,,
torchvision/models/mobilenet.py,,
torchvision/models/mobilenetv2.py,,
torchvision/models/mobilenetv3.py,,
torchvision/models/optical_flow/__init__.py,,
torchvision/models/optical_flow/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-311.pyc,,
torchvision/models/optical_flow/_utils.py,,
torchvision/models/optical_flow/raft.py,,
torchvision/models/quantization/__init__.py,,
torchvision/models/quantization/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-311.pyc,,
torchvision/models/quantization/googlenet.py,,
torchvision/models/quantization/inception.py,,
torchvision/models/quantization/mobilenet.py,,
torchvision/models/quantization/mobilenetv2.py,,
torchvision/models/quantization/mobilenetv3.py,,
torchvision/models/quantization/resnet.py,,
torchvision/models/quantization/shufflenetv2.py,,
torchvision/models/quantization/utils.py,,
torchvision/models/regnet.py,,
torchvision/models/resnet.py,,
torchvision/models/segmentation/__init__.py,,
torchvision/models/segmentation/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-311.pyc,,
torchvision/models/segmentation/_utils.py,,
torchvision/models/segmentation/deeplabv3.py,,
torchvision/models/segmentation/fcn.py,,
torchvision/models/segmentation/lraspp.py,,
torchvision/models/shufflenetv2.py,,
torchvision/models/squeezenet.py,,
torchvision/models/swin_transformer.py,,
torchvision/models/vgg.py,,
torchvision/models/video/__init__.py,,
torchvision/models/video/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-311.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-311.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-311.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-311.pyc,,
torchvision/models/video/mvit.py,,
torchvision/models/video/resnet.py,,
torchvision/models/video/s3d.py,,
torchvision/models/video/swin_transformer.py,,
torchvision/models/vision_transformer.py,,
torchvision/nvjpeg64_12.dll,,
torchvision/ops/__init__.py,,
torchvision/ops/__pycache__/__init__.cpython-311.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-311.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-311.pyc,,
torchvision/ops/__pycache__/_utils.cpython-311.pyc,,
torchvision/ops/__pycache__/boxes.cpython-311.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-311.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-311.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-311.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/misc.cpython-311.pyc,,
torchvision/ops/__pycache__/poolers.cpython-311.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-311.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-311.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-311.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-311.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-311.pyc,,
torchvision/ops/_box_convert.py,,
torchvision/ops/_register_onnx_ops.py,,
torchvision/ops/_utils.py,,
torchvision/ops/boxes.py,,
torchvision/ops/ciou_loss.py,,
torchvision/ops/deform_conv.py,,
torchvision/ops/diou_loss.py,,
torchvision/ops/drop_block.py,,
torchvision/ops/feature_pyramid_network.py,,
torchvision/ops/focal_loss.py,,
torchvision/ops/giou_loss.py,,
torchvision/ops/misc.py,,
torchvision/ops/poolers.py,,
torchvision/ops/ps_roi_align.py,,
torchvision/ops/ps_roi_pool.py,,
torchvision/ops/roi_align.py,,
torchvision/ops/roi_pool.py,,
torchvision/ops/stochastic_depth.py,,
torchvision/transforms/__init__.py,,
torchvision/transforms/__pycache__/__init__.cpython-311.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-311.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-311.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-311.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-311.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-311.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-311.pyc,,
torchvision/transforms/__pycache__/functional.cpython-311.pyc,,
torchvision/transforms/__pycache__/functional_pil.cpython-311.pyc,,
torchvision/transforms/__pycache__/functional_tensor.cpython-311.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-311.pyc,,
torchvision/transforms/_functional_pil.py,,
torchvision/transforms/_functional_tensor.py,,
torchvision/transforms/_functional_video.py,,
torchvision/transforms/_presets.py,,
torchvision/transforms/_transforms_video.py,,
torchvision/transforms/autoaugment.py,,
torchvision/transforms/functional.py,,
torchvision/transforms/functional_pil.py,,
torchvision/transforms/functional_tensor.py,,
torchvision/transforms/transforms.py,,
torchvision/transforms/v2/__init__.py,,
torchvision/transforms/v2/__pycache__/__init__.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-311.pyc,,
torchvision/transforms/v2/_augment.py,,
torchvision/transforms/v2/_auto_augment.py,,
torchvision/transforms/v2/_color.py,,
torchvision/transforms/v2/_container.py,,
torchvision/transforms/v2/_deprecated.py,,
torchvision/transforms/v2/_geometry.py,,
torchvision/transforms/v2/_meta.py,,
torchvision/transforms/v2/_misc.py,,
torchvision/transforms/v2/_temporal.py,,
torchvision/transforms/v2/_transform.py,,
torchvision/transforms/v2/_type_conversion.py,,
torchvision/transforms/v2/_utils.py,,
torchvision/transforms/v2/functional/__init__.py,,
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-311.pyc,,
torchvision/transforms/v2/functional/_augment.py,,
torchvision/transforms/v2/functional/_color.py,,
torchvision/transforms/v2/functional/_deprecated.py,,
torchvision/transforms/v2/functional/_geometry.py,,
torchvision/transforms/v2/functional/_meta.py,,
torchvision/transforms/v2/functional/_misc.py,,
torchvision/transforms/v2/functional/_temporal.py,,
torchvision/transforms/v2/functional/_type_conversion.py,,
torchvision/transforms/v2/functional/_utils.py,,
torchvision/tv_tensors/__init__.py,,
torchvision/tv_tensors/__pycache__/__init__.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_image.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_mask.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_video.cpython-311.pyc,,
torchvision/tv_tensors/_bounding_boxes.py,,
torchvision/tv_tensors/_dataset_wrapper.py,,
torchvision/tv_tensors/_image.py,,
torchvision/tv_tensors/_mask.py,,
torchvision/tv_tensors/_torch_function_helpers.py,,
torchvision/tv_tensors/_tv_tensor.py,,
torchvision/tv_tensors/_video.py,,
torchvision/utils.py,,
torchvision/version.py,,
torchvision/zlib.dll,,
torchvision\_C.pyd,sha256=4a5rfiSSohq2Jdgz1m4ekpmV03KmiXj_7Ql3zhgD4R0,6094336
torchvision\__init__.py,sha256=GH5ATaN0Zkz9Lh2FAfjOfqtPR0YgegQpxmdRSca-VKo,3471
torchvision\_internally_replaced_utils.py,sha256=jnEH2UiEKQCUf1Bq2fITPKxxg-ZySJONIVoniEh8hGY,1439
torchvision\_meta_registrations.py,sha256=fmCVLCp6N2AwWyDKjYlkjqEDbz0aklkvMN7EwEkHHBo,1663
torchvision\_utils.py,sha256=kcSn6P3Vjv5QPgHHNmNu59Mh-NLb5MDlYxvDkNJWrOM,966
torchvision\cudart64_12.dll,sha256=-GvH9gQ7VEH5aGhiQD2zXqIAbYc4GjWd7qNZmATGuD4,527872
torchvision\datasets\__init__.py,sha256=wzvgEzctcLQpmlMTsoOGUvWKLhYdsVtMT3gy7XXmW-A,3678
torchvision\datasets\_optical_flow.py,sha256=wgbioDD5F7kl9SsyqlmBDGOvzS18A_h26ZqtEkr5394,19980
torchvision\datasets\_stereo_matching.py,sha256=e1WS4MZQGhKGmPtnwc76Cl9cwDafK_xyzvYR16bLdi8,49998
torchvision\datasets\caltech.py,sha256=ZFJ-yA17AVDrG4mxZY5QcBgEf5oZX50_DzgMXd9_GMU,8975
torchvision\datasets\celeba.py,sha256=vAzuk7nzrml6FQd8kpGbi6eikdh6fc8oILhd24Uyyd8,8481
torchvision\datasets\cifar.py,sha256=yP0PumJE_tFnWbo3sd_QRdiEIJCFVfwR6wimPj41OJA,5956
torchvision\datasets\cityscapes.py,sha256=ZYOPir-rQjEcfYN2MAcJ9gLpouQK_9WA9b8vIbmOOOE,10459
torchvision\datasets\clevr.py,sha256=l2H_H4Jqmet68BqbgHehMZ65_GTLQkvGe9vRPfLNXLc,3504
torchvision\datasets\coco.py,sha256=fvHT5x62MhLG4pE8MUlpW-6g6_HEeizJjnIYZK3HxFw,4076
torchvision\datasets\country211.py,sha256=bn9zqsnigFSAnG893j-jTnwJ4zhrI53pFs9uuTX24TY,2459
torchvision\datasets\dtd.py,sha256=MBqZ8Yqwp5VRvPI_EvdDxb1nUeLM5QKVotSbGVzvpFc,4075
torchvision\datasets\eurosat.py,sha256=qTI24oTjJVXlfs5VHjvjC_3If9s9joobS1X7mbxswII,2111
torchvision\datasets\fakedata.py,sha256=eEiZFwVkcd0Zo-eps8AMRUkUpGKj9eQDZ6hoDxqsln0,2548
torchvision\datasets\fer2013.py,sha256=tot-6jE7_y8VRmugSUhGTPWi2O7puVyZJ90-Tb4pZ7M,2837
torchvision\datasets\fgvc_aircraft.py,sha256=f6gTdoOaeT6yJmbsunajf3abq5XsYfqHijaYaT2pPT0,4680
torchvision\datasets\flickr.py,sha256=2MUvshyI3BfmEHja9yao_Fq5pqK9A_mXxxjvf4o3Sb0,5505
torchvision\datasets\flowers102.py,sha256=be6QEIllm_-Tr3fhXohU0_mCBGOJp-IWcVFfgPZi_2k,4719
torchvision\datasets\folder.py,sha256=PdB8r5mjBZC4IGrVzRcCf7zwHF0pF3RVCJF8m1SFvDI,12227
torchvision\datasets\food101.py,sha256=E0Ikk7nswdswkjWdxN1VwOA_drD6WBOSOegsUCnluXs,3810
torchvision\datasets\gtsrb.py,sha256=3MpXIXkf9NiOa3Km75dRbjlx_uUqYtt-sQIGNKzT8gE,3845
torchvision\datasets\hmdb51.py,sha256=iQX_BQ_jdx0Oma2zCi4K9DTUZWkN6CI4ppIR_cDZxgg,6060
torchvision\datasets\imagenet.py,sha256=_VViJiUQ-UK28zrFu6x9n1XCc1WtVb5DhC_gAsvziic,8705
torchvision\datasets\inaturalist.py,sha256=G9SROR4dKUXv7K-MrR14vXFxw0aMRMmSkkPlZIf0lfA,10348
torchvision\datasets\kinetics.py,sha256=g3DEbxUZRjQou-EYSSgWrY8Ap34G-X0m19XWVnFnlUM,10580
torchvision\datasets\kitti.py,sha256=yuX-fPZN7Rs08ZFWkOXNn9EXEng0oqO8H1rsEFcPyF0,5758
torchvision\datasets\lfw.py,sha256=rRhxwkhqHXkfeybRSZ9pszAWZUhSdWzwBMyqBTiYV8Y,10746
torchvision\datasets\lsun.py,sha256=EkIlHlrmaKBLjCxKGFwYKIiYQkfsPxFWnUomGlfOchU,5842
torchvision\datasets\mnist.py,sha256=Jafwq8nwGtVo2jqSUU8oBRJrevttXfSQ-gu-rWdGC9c,21753
torchvision\datasets\moving_mnist.py,sha256=t-E3GLqVjph9kLld7iHikgucYZNNXlSAteY8iZ8Epwk,3678
torchvision\datasets\omniglot.py,sha256=th4Rfpgz0b4O-3nKjwzJz4if0Xdn0oe3v1qz5gViN7w,4193
torchvision\datasets\oxford_iiit_pet.py,sha256=ny1u1p1e3ihlpMEiYxbefFZ8hcXaHV7NZ2s7XI002JM,5178
torchvision\datasets\pcam.py,sha256=XGYE-jz3C52-PaRlU0zAJYmOK7MPGxdgJXC2e28c-Ts,5245
torchvision\datasets\phototour.py,sha256=kiBA1S-06U0qWxwgG06lVFiqRpC12z-fzD_IOo1y8VA,8152
torchvision\datasets\places365.py,sha256=zejpHPKc7qRyaaXw_GIeSxY3Z_x1f2U6NWn1YVFHtng,7369
torchvision\datasets\rendered_sst2.py,sha256=UFEfUE8AaiJkGjE6cuEdq-yfuK3c_bzAruJ7bmtSUUo,3648
torchvision\datasets\samplers\__init__.py,sha256=yX8XD3h-VwTCoqF9IdQmcuGblZxvXb2EIqc5lPqXXtY,164
torchvision\datasets\samplers\clip_sampler.py,sha256=ERp9c0O3ZGQNR3lLXvZXWjfJkXayYym8bhYfOm_MNKI,6416
torchvision\datasets\sbd.py,sha256=Bl0uvVerxkh1vk5Wq11gWzo9jrERVsPThK5sxIai1X4,5325
torchvision\datasets\sbu.py,sha256=4HuHhYrehuY6MMZO10gDRQB8p7K9niv2uBzbVgJzxn0,4190
torchvision\datasets\semeion.py,sha256=Gsu8T1abeE0t-0lHf8TACuXh09BruHExjiw3d-bEZaQ,3179
torchvision\datasets\stanford_cars.py,sha256=-vBokYn4GHz8dvmN5GfV5teXBL1sLnYSk9MZHPV7RZI,4964
torchvision\datasets\stl10.py,sha256=oCfFz7K3jWOqaU-DiD5vQkEZ-fOKvWqUfe_fNs2xpWs,7407
torchvision\datasets\sun397.py,sha256=R09gTk7RveLMnnjFCtxhrzRdWMMwp_dSe-VYIrGcV38,2824
torchvision\datasets\svhn.py,sha256=6VW7Sk3WC_TTPiWJZac_1yKnVPp27QFfriUIm9_F6PE,4897
torchvision\datasets\ucf101.py,sha256=45HG1gpwd1CwYJhF4YJqdb83e8L88v1UHP2KMh08Yo4,5602
torchvision\datasets\usps.py,sha256=PyFECpMSh0sMswf-rZFpzy-Uyopa2BLMEYKLJgGWBRE,3535
torchvision\datasets\utils.py,sha256=l4AcxUzBC-Ipsjo990Y04NPWRWpT0XxLygsvJUZoxOE,18855
torchvision\datasets\video_utils.py,sha256=w3lr-Oz392GBMqH63KxC62Vw-fCMlfgEUe24JGxYZpo,17483
torchvision\datasets\vision.py,sha256=rp3GrpyzhSOYz_AyCOZb1GrR2-8MsbZs_iy3nbX5Eyw,4245
torchvision\datasets\voc.py,sha256=QFt-ALxLyeSFi-pNM191DOt5HKQz5ET_MHGTyyddlgU,8984
torchvision\datasets\widerface.py,sha256=RB01OqpsxGJsI-ItVJolYN24FnFRW1oS7ibuK7yE_Ko,8308
torchvision\extension.py,sha256=0A4efQ6V8RlQcMMtDpK74VIBHpDv4icjkkOc-EokPHw,3233
torchvision\image.pyd,sha256=HTLso3E9l--XNkfkjqOr_BCZcN2-5Okar0KiudWguN8,158208
torchvision\io\__init__.py,sha256=md51PMqDbCY8Wvo9rA2il7ZrZ87GshTq8fJY3xhNVOA,1547
torchvision\io\_load_gpu_decoder.py,sha256=B3mPLXerJYXqiHv9FO2laRrGRlIkXii7CsD0J8J_SwU,182
torchvision\io\_video_opt.py,sha256=n5PL4hXCnOVaLDlSfLtWzP8yK4ypwWbyvQhbSHrO0Ps,20902
torchvision\io\image.py,sha256=L1W5UTvvrMPbHBjl0fbVAuyhHCwtmCM2WL1yP9NeGE8,10142
torchvision\io\video.py,sha256=tcDKnx2z_AXAgBU5CCgthNGL2lMBDoMT-vXnfW8qyzE,16089
torchvision\io\video_reader.py,sha256=3s0LX2XxLhR_n9aLCkRwVcmXwL4zM5NsOSxdDjPb3Lc,12045
torchvision\jpeg8.dll,sha256=aM-Kj2MkrdHI0gkgpHfh86_icuM26XiMu6gyMGeuKig,552448
torchvision\libpng16.dll,sha256=nPxu7uIrOxgpEXCLUyjP7rQBCghBVPsL-h8OxeArvc0,192512
torchvision\models\__init__.py,sha256=6QlTJfvjKcUmMJvwSapWUNFXbf2Vo15dVRcBuNSaYko,888
torchvision\models\_api.py,sha256=RJurpplK_q7teeUlnqlMuTTxAorqIEhEcuQydyhzF3s,10331
torchvision\models\_meta.py,sha256=2NSIICoq4MDzPZc00DlGJTgHOCwTBSObSTeRTh3E0tQ,30429
torchvision\models\_utils.py,sha256=X7zduE90fkek8DjukzyENOcZ0iop03R0LIxC_FuAazk,11149
torchvision\models\alexnet.py,sha256=XcldP2UuOkdOUfdxitGS8qHzLH78Ny7VCzTzKsaWITU,4607
torchvision\models\convnext.py,sha256=mML3XALGvQGe3aYkmz6dbC7Wus7ntZWr7IVihWjLV8M,15740
torchvision\models\densenet.py,sha256=4NtnAY6M2xYQZzlXYyR_8LN68OMVx_3XKzfoISHVLmo,17252
torchvision\models\detection\__init__.py,sha256=D4cs338Z4BQn5TgX2IKuJC9TD2rtw2svUDZlALR-lwI,175
torchvision\models\detection\_utils.py,sha256=m9bowqjuYiR9A7HI7wJAK_kgFrUYlKSukXOEwuUtRpA,22667
torchvision\models\detection\anchor_utils.py,sha256=TQKWOKDFALsTmYw_BMGIDlT9mNQhukmgnNdFuRjN49w,12127
torchvision\models\detection\backbone_utils.py,sha256=acTcpOtAHRYDTx7xx54Dk2EBy3ePkizwJWLWICuBxjw,10691
torchvision\models\detection\faster_rcnn.py,sha256=SywC6HZlhsAuykDRp1qCfe3OZbtB7wAxqlpNGqxZAl8,37610
torchvision\models\detection\fcos.py,sha256=8ffrmOs2WZZFlUCzoOV5NiGkDZ7K24CuJozvX0bhNWg,34761
torchvision\models\detection\generalized_rcnn.py,sha256=nLVj2o8yr6BW1MN12u30QuFbvtsdllEbUlbNH-dO-G0,4861
torchvision\models\detection\image_list.py,sha256=IzFjxIaMdyFas1IHPBgAuBK3iYJOert5HzGurYJitNk,808
torchvision\models\detection\keypoint_rcnn.py,sha256=E-kvKlT5U6zCyBd9CRZUnKpi0fkmZNYUoaOaaaEo5FE,22232
torchvision\models\detection\mask_rcnn.py,sha256=moCC06BMM7Xd5e61PcZztfBY-dLO0mOZwKFUFG3WC94,27088
torchvision\models\detection\retinanet.py,sha256=Erd9q38DhUGCc7NLJxKGSBUafgEUBe1fA5zaEECwm2g,37954
torchvision\models\detection\roi_heads.py,sha256=f_Lde69JHugGAaiGWh6ugJ9WUTT8f7InxPry_MZxgZY,34698
torchvision\models\detection\rpn.py,sha256=zQ4S0EchqmRPVUDAoKQV3O4gK2b-n-AlwyihEp6yikE,16122
torchvision\models\detection\ssd.py,sha256=QyWW7IihpNG4_SWeVo1-X5J9kDJrasd794-AHIcDxQY,29661
torchvision\models\detection\ssdlite.py,sha256=FvorXN6B7d7JAbSt1x_RnVr6T6xLKHpoxCdsdO22xZI,13546
torchvision\models\detection\transform.py,sha256=S-plgAuwYsb2-kdHb2FGQjpVk-GRY3fzJOyVAbL-6GU,12435
torchvision\models\efficientnet.py,sha256=tW6BpsBProhN6b1o1_sHSikSoqDupQRgdgRETiYjcAY,44221
torchvision\models\feature_extraction.py,sha256=uTBD0Obc42BSm0YBU7VcSKJR7HDeY6Da5nOiuGBZsV8,26140
torchvision\models\googlenet.py,sha256=AtXckNXKcmWhDyoozVsvb3VPI-odl2tptiYz7KXU3wA,13151
torchvision\models\inception.py,sha256=l9tutwO7KNVa5nfdl1_5f-6lJzQ-NSOCzXPArDILeAA,19329
torchvision\models\maxvit.py,sha256=DWVocs3Jy3vtSUiHuL48Xr202VqqnTHpdmz16nJoynU,32785
torchvision\models\mnasnet.py,sha256=PTSF4DchbFgtOchd9kgoPKCcfKH6NC4WfHa5bv4jZ58,18008
torchvision\models\mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision\models\mobilenetv2.py,sha256=eVil23yP4f-DBUhjKt73mao84zEiF5Y01wHNwh8HCdM,9970
torchvision\models\mobilenetv3.py,sha256=g1Ul1RkohPHhXfsecg2r0W7NVlEgVUc7X_uT1bxxrTQ,16702
torchvision\models\optical_flow\__init__.py,sha256=uuRFAdvcDobdAbY2VmxEZ7_CLH_f5-JRkCSuJRkj4RY,21
torchvision\models\optical_flow\_utils.py,sha256=PRcuU-IB6EL3hAOLiyC5q-NBzlvIKfhSF_BMplHbzfY,2125
torchvision\models\optical_flow\raft.py,sha256=o9wJ3jZH9EWwJl7fQYeWSdeXPMKYOZ0Zwm-hhcequVk,40942
torchvision\models\quantization\__init__.py,sha256=YOJmYqWQTfP5P2ypteZNKQOMW4VEB2WHJlYoSlSaL1Y,130
torchvision\models\quantization\googlenet.py,sha256=P92cacoKVTV4cDoaNkRevLx1daGH5DkPfUwPDMuOXO0,8290
torchvision\models\quantization\inception.py,sha256=TXz2hRpSvh6zYP398MsXTYQMnqYgnYnq5wyG6xr5Nhk,11088
torchvision\models\quantization\mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision\models\quantization\mobilenetv2.py,sha256=g2z3HPQ0MXFCuMV5TpLPRdO99m3wC_3d0ukUanXaJHo,6037
torchvision\models\quantization\mobilenetv3.py,sha256=l9g1AwKiU35XKuobXo-UPe5MqRKC1kgN7xgWWim4qr4,9467
torchvision\models\quantization\resnet.py,sha256=azvn1vwebP22qryYGNgFLMviGjHklXOX7xd4C2cggUo,18423
torchvision\models\quantization\shufflenetv2.py,sha256=7k9MLRLzjP3vke-e0Ai_cnA-j15KGQq5yDcs_ELXUg8,17311
torchvision\models\quantization\utils.py,sha256=Ij88l6toyO8MQi1w512Jt-yQ2Q9hK75-Z2SOjIzS6Zw,2109
torchvision\models\regnet.py,sha256=NbsA3RO7ka7k9fwYyVZ5wvvtJUuhXqyX76aGIoIujGE,65124
torchvision\models\resnet.py,sha256=AXWWl7XlkSQpUCsv8lCaWeDuYaq-KyOLXmBe0R8rv58,39917
torchvision\models\segmentation\__init__.py,sha256=TLL2SSmqE08HLiv_yyIWyIyrvf2xaOsZi0muDv_Y5Vc,69
torchvision\models\segmentation\_utils.py,sha256=yFeyBa5_Pyv1UQ_2N64XMRgTYsxifwzDd-VRP-vmIGM,1234
torchvision\models\segmentation\deeplabv3.py,sha256=TDnk5uZJ6cN1BOjRrV2Hzc8DO3NZKQKBuW1c2YwpXyM,15353
torchvision\models\segmentation\fcn.py,sha256=mQ1Wi4S9j5G6OQbNciuxNwVbJ6e9miTzIWj6mUF5JwA,9205
torchvision\models\segmentation\lraspp.py,sha256=yx_b3PJsH5e0F3TqiGDhEnbXCGTdNX2iIxsKvNenM0s,7821
torchvision\models\shufflenetv2.py,sha256=VEGsTNNTdqdu8m7I62zQuJK_5CkET-0Y4ixYBJ-QBCs,15852
torchvision\models\squeezenet.py,sha256=Dha-ci350KU15D0LS9N07kw6MlNuusUHSBnC83Ery_E,8986
torchvision\models\swin_transformer.py,sha256=-Q9Kd1qzsD7vL3u137Q4MoHSTwzA6QFcweaF0zCWmUk,40370
torchvision\models\vgg.py,sha256=Qt9r5sFoY-oPdwP4De61jqSVe9XUZLXK47r9yVDQ33M,19736
torchvision\models\video\__init__.py,sha256=xHHR5c6kP0cMDXck7XnXq19iJL_Uemcxg3OC00cqE6A,97
torchvision\models\video\mvit.py,sha256=xIK4nCOuJWXQjoX8NzcouwzyTkIkcFug3yiu0a5-Dk8,33895
torchvision\models\video\resnet.py,sha256=JOP7FDfUOfQQP-jEYUvzSIOr9Iaexl2L3iUh-rzrp90,17274
torchvision\models\video\s3d.py,sha256=Rn-iypP13jrETAap1Qd4NY6kkpYDuSXjGkEKZDOxemI,8034
torchvision\models\video\swin_transformer.py,sha256=M74P2v4lVKM6zgwoeFn_njppZB2l-gAjuGVvzzESKpU,28431
torchvision\models\vision_transformer.py,sha256=GE-_-dlFJQPTnONe4qrzYOYp-wavPOrFPCo9krM39Vg,33000
torchvision\nvjpeg64_12.dll,sha256=OoVfoHF8HNKpej9ia11ZOrhg_mtP3BsgJg84ZR1TX_4,4796416
torchvision\ops\__init__.py,sha256=7wibGxcF1JHDviSNs9O9Pwlc8dhMSFfZo0wzVjTFnAY,2001
torchvision\ops\_box_convert.py,sha256=glF6sulLzaw_KG36wg0CHWt0ef62BnkjokbqQnBUMsU,2490
torchvision\ops\_register_onnx_ops.py,sha256=g4M5Fp7n_5ZTzIQcUXvEct3YFlUMPNVSQQfBP-J0eQQ,4288
torchvision\ops\_utils.py,sha256=xFrLnLhKDHiG2TN39tUWY-MJbLEPka6dkaVVJFAN7-8,3736
torchvision\ops\boxes.py,sha256=LtwFsa6GDIs4nSxSIpQ48sza1ucvJGfQCHmreSoul5s,15912
torchvision\ops\ciou_loss.py,sha256=Qzm89C82ehX-YvYBPLPRPhbJZdr3itizxuxrT7MLi9o,2834
torchvision\ops\deform_conv.py,sha256=DuIosFDK3tsY5RlHU7mez5x1p08IQai9WG14z3S0gzU,7185
torchvision\ops\diou_loss.py,sha256=6IebWlMYc_2YnbG36niDXgM16vxajSKRfiusEuUJwpQ,3456
torchvision\ops\drop_block.py,sha256=ZkIzM1b3v5_U7z0eavzaNpN7IBq0N4ZNwwvWArispwg,6010
torchvision\ops\feature_pyramid_network.py,sha256=Jts5mzUJX3EarcAQU5MDUe0a5Sgn5YjUstaW2JQpgEE,8952
torchvision\ops\focal_loss.py,sha256=lS5FqgLFuDKlpm0sk5V1VgIA6LFAdJUXQaPi35nEDoU,2319
torchvision\ops\giou_loss.py,sha256=xB_RlES28k_A6iH2VqWAPBQkiT_zkEwdtREDGR_nVJM,2772
torchvision\ops\misc.py,sha256=niQnKPuifQzVXAWnnf6TkVsgetqyetjZ6lq3wi2tsZw,13892
torchvision\ops\poolers.py,sha256=sfgcZWh2dIo9UY437CnpAHdxqPQhuvjNPYzhIKlAIPE,12247
torchvision\ops\ps_roi_align.py,sha256=6_kmnE6z_3AZZ1N2hrS_uK3cbuzqZhjdM2rC50mfYUo,3715
torchvision\ops\ps_roi_pool.py,sha256=2JrjJwzVtEeEg0BebkCnGfq4xEDwMCD-Xh915mvNcyI,2940
torchvision\ops\roi_align.py,sha256=xCwgOCqGfn1WYnJ9krI_ch9_i332gw8YTub1QwO5b84,11030
torchvision\ops\roi_pool.py,sha256=cN7rSCQfpUzETvP8SjPDl1yfXjbxg9I-tXnHbvAKya8,3015
torchvision\ops\stochastic_depth.py,sha256=9T4Zu_BaemKZafSmRwrPCVr5aaGH8tmzlsQAZO-1_-Y,2302
torchvision\transforms\__init__.py,sha256=WCNXTJUbJ1h7YaN9UfrBSvt--ST2PAV4sLICbTS-L5A,55
torchvision\transforms\_functional_pil.py,sha256=UEiaElYLuYXkNR__O_dbKts2BKBsb28Rj50RMFgRxig,12505
torchvision\transforms\_functional_tensor.py,sha256=O5wwyKkMnEkZaK9Z_CiGZJpstRlnsbQBIjQKEqyL_r8,34927
torchvision\transforms\_functional_video.py,sha256=c4BbUi3Y2LvskozFdy619piLBd5acsjxgogYAXmY5P8,3971
torchvision\transforms\_presets.py,sha256=AZ381d33VDxOE_9x2CVFxtiAKmxW6uOirKGCXv08wUI,8735
torchvision\transforms\_transforms_video.py,sha256=ub2gCT5ELiK918Bq-Pp6mzhWrAZxlj7blfpkA8Dhb1o,5124
torchvision\transforms\autoaugment.py,sha256=UD8UBlT4dWCIQaNDUDQBtc0osMHHPQluLr7seZJr4cY,28858
torchvision\transforms\functional.py,sha256=i1AbspHXeNb0-E36-kUUdg2zii2nvxSVKLmrdz3BJVk,71077
torchvision\transforms\functional_pil.py,sha256=UIc9SyotbfzE1KdN9xt6kHMIe-UlnrZJrmtRvpShXWc,386
torchvision\transforms\functional_tensor.py,sha256=Ww9_-tsjXhP2N9oigKcmcUVH8F24_rq3gniTglCn8FI,392
torchvision\transforms\transforms.py,sha256=XkmsLBWqiOdx29DoSwzC9Lgxl7XGlSUWf6KH3S02l3w,88259
torchvision\transforms\v2\__init__.py,sha256=aLPrjLPN_xmYBvaRyXfxK8jaHSfnUs3-bFnrkE5n8Ss,1476
torchvision\transforms\v2\_augment.py,sha256=00YWcYnbXS9QJ5ZDwhz5Fapngq9bL7eh9yZvKERpX2U,14268
torchvision\transforms\v2\_auto_augment.py,sha256=jJ0kj69bXQ8Z_UJBW8sGJp6qGKizYhBedxTSJiCwd3k,32625
torchvision\transforms\v2\_color.py,sha256=aYaCgzkvCp1GQXT7SRON2-gLrD9uFhJnPbtMlo8eqVs,17356
torchvision\transforms\v2\_container.py,sha256=LzzMbZ2wo5erdnjbsC1qOFEs-KuxM10phVhkfyI3wGE,6304
torchvision\transforms\v2\_deprecated.py,sha256=srPkH0mDM0VuUO7-trHu6OjBMSVtFWTSgI02-N1DxMA,1936
torchvision\transforms\v2\_geometry.py,sha256=H2eYTXHybFMqwHL1V8D9EPOehcKU3C7SIKt0CQkwpWA,70320
torchvision\transforms\v2\_meta.py,sha256=Ois06Ja0FKOMInbGa0pjV87xSwXSKuTBD1tZQgfY_aA,1655
torchvision\transforms\v2\_misc.py,sha256=eMPnIbM4Ht6ijWg3LX69cj9C8GnTvF5Pw8le4UztwpM,17926
torchvision\transforms\v2\_temporal.py,sha256=cuGEh-9W1wrgq5QA3SD0_Ti_gnHoCoPIm-1drdk9DV0,999
torchvision\transforms\v2\_transform.py,sha256=i3KWfXcFjioL3FV5ix1XetK1cLHyw3hutIWwd_wNn9w,8652
torchvision\transforms\v2\_type_conversion.py,sha256=og2Tm1lEQPXvA7qFK-Mqs0x0lUjx8Xf_cdgGthXQaQA,3157
torchvision\transforms\v2\_utils.py,sha256=GDk0wZcoMAe5p8j2NW5IbWuiftXaStc2TKXEM3Ju-qY,8928
torchvision\transforms\v2\functional\__init__.py,sha256=KGc7-5_q8cDSeARWlAxdbt0nMZeMH8aHyiYSjtYO_1o,4394
torchvision\transforms\v2\functional\_augment.py,sha256=95WH21rtrJvhtiRkH5t7mOv045t2MzmCaq00pW9dtmg,1743
torchvision\transforms\v2\functional\_color.py,sha256=5r-Uu4I2pdoJb89lOShy7janGiJxeo5EDY5sERI8lgc,29747
torchvision\transforms\v2\functional\_deprecated.py,sha256=AEyiMWV2HZeJJrLssWDVMU2lD7ZK99zI76fVHN0f2Gg,832
torchvision\transforms\v2\functional\_geometry.py,sha256=-3jrmFnP7k1dGVuTZXgIx6rhJLXBXtjBlX2-9ObQxS0,89268
torchvision\transforms\v2\functional\_meta.py,sha256=egjDfMNaEneDyzwD2WaAupQj82X4PmzCPfvlID4W0c0,10643
torchvision\transforms\v2\functional\_misc.py,sha256=JNlsKoZ4vbsxFt_CCWqqzDgWQFY6-wMwJEyNmIQOGUU,11011
torchvision\transforms\v2\functional\_temporal.py,sha256=x2JU34EJtOTSVPNyk_DBXPHcwSberBXpxchJrbHjzuw,1170
torchvision\transforms\v2\functional\_type_conversion.py,sha256=I764NCg5l7DThaQBwlK3V7f4NIOqxjyITQTOD3DVGQo,848
torchvision\transforms\v2\functional\_utils.py,sha256=W58LavR0-3C-bV2fpDf3Rvn2_7GskRpcz4qWEKgh1pw,5627
torchvision\tv_tensors\__init__.py,sha256=ptItIlEuTr8u3xH4DqnkO9gvpdYWuLaD_G-w3GjNu2I,1291
torchvision\tv_tensors\_bounding_boxes.py,sha256=TarA2yNxmx7jK8HFiLlN4dUZA1Pl6q-CSBc99hmYekE,4588
torchvision\tv_tensors\_dataset_wrapper.py,sha256=hEuNEamUjbSpWpcfGnz_4vr5AfWiS8OhNK7OZj-5F6g,24924
torchvision\tv_tensors\_image.py,sha256=YHw-MWAIsmXhETj-1vqVXg5INmJa7t3JwVGhlsLoYXE,1964
torchvision\tv_tensors\_mask.py,sha256=-OaP8Apq_UKlGrE1tQjSihP6x5sRMT3o9R6bgcv7Rr4,1497
torchvision\tv_tensors\_torch_function_helpers.py,sha256=J4KE56WUwXYu04kB1Zf6WcPq4Nu0A_NYbsFgSTZv5lc,2355
torchvision\tv_tensors\_tv_tensor.py,sha256=J-rJHEXCTa0fkZzFxe2J3OphClqaA5X-ZJ9Iu1MbjQM,6387
torchvision\tv_tensors\_video.py,sha256=eoVOPHz2B0kD6VcAWq3v-v6_MYsv5Oa4yHXkO5BuEvo,1427
torchvision\utils.py,sha256=PVSYbCAjRMpD-qwVVjKcOvEv_Hxfw7uzvZXM4F1vpBU,24109
torchvision\version.py,sha256=2pWU_IUzRhMm9Bf6djtN_CM5rvoJ-JpTBzRmhYBaWNg,208
torchvision\zlib.dll,sha256=VMdV5rkEmaXvSaZc6UclNShv5Ns1ZaBsSlP4pIM1Mtw,87552
