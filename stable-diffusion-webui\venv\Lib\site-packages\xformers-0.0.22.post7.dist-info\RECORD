xformers-0.0.22.post7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
xformers-0.0.22.post7.dist-info/LICENSE,sha256=xPdISGPi2k__n6EMnda0mDEa6uov8po3MbHeQX3dhCI,1645
xformers-0.0.22.post7.dist-info/METADATA,sha256=KRCImQ4_FkYv6ncZqr11c9Ublk7-8ssrmA0XcpWHDBE,1034
xformers-0.0.22.post7.dist-info/RECORD,,
xformers-0.0.22.post7.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers-0.0.22.post7.dist-info/WHEEL,sha256=badvNS-y9fEq0X-qzdZYvql_JFjI7Xfw-wR8FsjoK0I,102
xformers-0.0.22.post7.dist-info/top_level.txt,sha256=4Px1VcGhKk0j3XhKXjA8HtTm6EQOb0hazeJ5nQsNlKk,9
xformers/_C.pyd,sha256=kTafDZkPlFZdlB5qD8Z6ssFa2a1SX84qS0Bmf8HNago,183950336
xformers/_C_flashattention.pyd,sha256=L2oVa5VONwK7yFVaBEMesUTPEYDAoLf5O8c0OTIlYYA,270882304
xformers/__init__.py,sha256=x6mlp-Jw41Q3QfnLwOrY2MH1Vknzbwv_QmWd2a0kOtU,1457
xformers/__pycache__/__init__.cpython-311.pyc,,
xformers/__pycache__/_cpp_lib.cpython-311.pyc,,
xformers/__pycache__/_deprecation_warning.cpython-311.pyc,,
xformers/__pycache__/checkpoint.cpython-311.pyc,,
xformers/__pycache__/info.cpython-311.pyc,,
xformers/__pycache__/test.cpython-311.pyc,,
xformers/__pycache__/utils.cpython-311.pyc,,
xformers/__pycache__/version.cpython-311.pyc,,
xformers/_cpp_lib.py,sha256=iU1DbT-lQriQa4fDCwIzNAgKtS9afKYifJIhIynvS3c,4736
xformers/_deprecation_warning.py,sha256=oASW0P7S8aHENqyPNRkgReNZimZZKLYlLxFF_GxRUX8,468
xformers/_flash_attn/__init__.py,sha256=XQ8sL80b7wlsa599lbVcb2FPy2DbTtjteoVYE3od2pQ,296
xformers/_flash_attn/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/bert_padding.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_interface.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_triton.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_triton_og.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_blocksparse_attention.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_blocksparse_attn_interface.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/fused_softmax.cpython-311.pyc,,
xformers/_flash_attn/bert_padding.py,sha256=GwEEim6-fbOFU7ektdDmOVq9MfqnF91PDo9d6PVe5WA,9333
xformers/_flash_attn/flash_attn_interface.py,sha256=PdUAU7_RQe1pWSD4P4x0OxqfIkQju6Yn21tQ1lTV4es,40694
xformers/_flash_attn/flash_attn_triton.py,sha256=W2NIPsie2qdcCfRcOcEqks8swWQFYgVOigfoOyFHq_4,42272
xformers/_flash_attn/flash_attn_triton_og.py,sha256=LZm4Jlz0ECHtuOWEnpe69_iqUrNfNqGYT6AI5D4X5Qk,11693
xformers/_flash_attn/flash_blocksparse_attention.py,sha256=Skvo18pafafRcBPVU3tT8rRF-ofrEgUq_-Uu8pbpu34,7666
xformers/_flash_attn/flash_blocksparse_attn_interface.py,sha256=3z54--DCBdcS7cqF0oiC1Ux53Ye8o-TwbdSgdGJSea0,7465
xformers/_flash_attn/fused_softmax.py,sha256=-ZMBHj_1CjfOOZwsP9D1w1CZstUyRUVONUhz_rD5cAE,7994
xformers/_flash_attn/layers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/layers/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/layers/__pycache__/patch_embed.cpython-311.pyc,,
xformers/_flash_attn/layers/__pycache__/rotary.cpython-311.pyc,,
xformers/_flash_attn/layers/patch_embed.py,sha256=_2b237fpHQa2Q6lggjVHKllo1I7ofNju7ZugWlZieqQ,2203
xformers/_flash_attn/layers/rotary.py,sha256=7fnowvZ3Usv8rcmRyWDHXivxoLZRGLUYw2T5G1vJH-I,19334
xformers/_flash_attn/losses/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/losses/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/losses/__pycache__/cross_entropy.cpython-311.pyc,,
xformers/_flash_attn/losses/cross_entropy.py,sha256=DcTvlebuoTYXDytOEySGgcsFW2IaBfEnkUR911npQAw,2378
xformers/_flash_attn/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/models/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/baichuan.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/bert.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/bigcode.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/falcon.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/gpt.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/gpt_neox.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/gptj.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/llama.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/opt.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/vit.cpython-311.pyc,,
xformers/_flash_attn/models/baichuan.py,sha256=zPekhp17ajAhjTihCVL8FY2X2YUzo8PaD8oA2NdEnS8,5949
xformers/_flash_attn/models/bert.py,sha256=_nD_KhCTIgIMdEhOosa80mlQFb7ROUlxjMkzggSLvAY,34056
xformers/_flash_attn/models/bigcode.py,sha256=D16KsDAurcLs6Spw2WUpzY6L03r-dv9735tTT-Y98js,9616
xformers/_flash_attn/models/falcon.py,sha256=Z8eFr6U7BaAOX0cElGRX9W-nZdgRVRI9NtSf5A3kT6Q,6176
xformers/_flash_attn/models/gpt.py,sha256=4At2niXnbRIncB3aFUeeGRsgdYTJX8M1ca3FdoPWsXs,48198
xformers/_flash_attn/models/gpt_neox.py,sha256=5umV4flMHfPE21N0hJVjckqrvATF3y5wx_WAtHMM4eA,5276
xformers/_flash_attn/models/gptj.py,sha256=mrJcKwuYQk6mGsFsV2-HA3Db79KP0LCbyIb3vnS9jbc,4545
xformers/_flash_attn/models/llama.py,sha256=Gu-fr9ltOFxKgUTCDHpgwNpTpFeA_d6MGzYT3jyG394,17003
xformers/_flash_attn/models/opt.py,sha256=w6LSHfxDBPC1d_CIyNUDAU1KifeynDh4WuipbYkUhDA,5280
xformers/_flash_attn/models/vit.py,sha256=eLRY8J0lIyCgRLSdOGIzs1U1hN-pEKRv8jWpUe4slX0,14509
xformers/_flash_attn/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/modules/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/block.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/embedding.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/mha.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/mlp.cpython-311.pyc,,
xformers/_flash_attn/modules/block.py,sha256=xbPsxiezwIefWdizmTLDNow36f1NcKurOG3G2tnhdFk,18424
xformers/_flash_attn/modules/embedding.py,sha256=3U2vTsd7aQXfD2wiDLGvEKhfVyw3kC7dtfxbnh1P6BY,8909
xformers/_flash_attn/modules/mha.py,sha256=MYoe7jyUkxy1NT_zYVxz3nesu67Ay-8FjdQ0E_gCkjw,40251
xformers/_flash_attn/modules/mlp.py,sha256=YHkBE7QMxh03EIEkJ19qSQ62L3FxRy1gyNIdzFXZfqM,6224
xformers/_flash_attn/ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/ops/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/activations.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/fused_dense.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/layer_norm.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/rms_norm.cpython-311.pyc,,
xformers/_flash_attn/ops/activations.py,sha256=CVIUh2Qouju4lM7sZQw1De0POjVFn84CrqED706-AnU,4071
xformers/_flash_attn/ops/fused_dense.py,sha256=IQL8zidCCObh1B_2tLPoPhErYWSTfjIyTnwc7xafa8c,28595
xformers/_flash_attn/ops/layer_norm.py,sha256=re3-HG7fv-qeZtehElszHG5sQKgH17GmSvX1bJmsPcw,23243
xformers/_flash_attn/ops/rms_norm.py,sha256=0YbzNABBn31R_7asugdJCFUzXZjvohk1XYkkPKeZ0_U,4162
xformers/_flash_attn/ops/triton/__init__.py,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
xformers/_flash_attn/ops/triton/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/cross_entropy.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/k_activations.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/linear.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/mlp.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/rotary.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/cross_entropy.py,sha256=8OulTQ_A1t_VaLSjnMZ_u6_5-N7xBAQ8IZHPp_E1Y78,11774
xformers/_flash_attn/ops/triton/k_activations.py,sha256=EXip4m6AwLI4f3Go1b3WRLg32RepR70SU_uBZCl_4co,4196
xformers/_flash_attn/ops/triton/linear.py,sha256=hyB5-xYqH0cKtPGq26bqzCZJxAPAkN6M8QorwcVFzKs,21435
xformers/_flash_attn/ops/triton/mlp.py,sha256=9U7E7QT9og5J7kQkSSxFMuBM5FUM-V18n1wOsaNfQL0,6217
xformers/_flash_attn/ops/triton/rotary.py,sha256=8-Ls0qlZQHTqMYW5ymhRGtOUgOBB_UszbkCEyIh-ySE,9230
xformers/_flash_attn/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/utils/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/benchmark.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/distributed.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/generation.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/pretrained.cpython-311.pyc,,
xformers/_flash_attn/utils/benchmark.py,sha256=gF2FiYpkQw7DsobteYnJXhXnEgZa6Q4sCVEyx5NxMZA,7416
xformers/_flash_attn/utils/distributed.py,sha256=1KKwHrmoAjlGA2OD3O5ntO5LmoT34Xq3X9xkxX8X1Wg,5969
xformers/_flash_attn/utils/generation.py,sha256=TpcZeiJhEo1YEKXTnO-rbXcplcwy3IqLVSgTY5W0v30,31444
xformers/_flash_attn/utils/pretrained.py,sha256=z3aA3mwarpBSRVpXZX8y8bxNFqwzGeON0k14TvZDYIU,3325
xformers/benchmarks/LRA/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/LRA/__pycache__/__init__.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/batch_fetch_results.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/batch_submit.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/run_grid_search.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/run_tasks.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/run_with_submitit.cpython-311.pyc,,
xformers/benchmarks/LRA/batch_fetch_results.py,sha256=P8_GIzJEONssaMGTnOhT-sYYga20lZxiIW5Bvx8ufB4,3604
xformers/benchmarks/LRA/batch_submit.py,sha256=spwxdzpH43ixNJSma-ZauJXXj1AHf5nrkqnDwgQaYa8,1734
xformers/benchmarks/LRA/code/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/LRA/code/__pycache__/__init__.cpython-311.pyc,,
xformers/benchmarks/LRA/code/__pycache__/dataset.cpython-311.pyc,,
xformers/benchmarks/LRA/code/__pycache__/model_wrapper.cpython-311.pyc,,
xformers/benchmarks/LRA/code/dataset.py,sha256=GxlIXt0cdpZJ-mA1pAnaOuSBoEpu4sLi3C6lxoK7FCQ,1449
xformers/benchmarks/LRA/code/model_wrapper.py,sha256=vFfNfBh1eyJvVs2GIr7n48KImatW3t0BSbHDrzGLICs,10065
xformers/benchmarks/LRA/run_grid_search.py,sha256=bZZoLui4Zor8WV1eryaaHAUqSjgv3b9AFJwO5E5lHbU,5475
xformers/benchmarks/LRA/run_tasks.py,sha256=pYHvjhJm4ANpAmC99iDzr-UFryaU7MnT7yHXuAl60XA,9528
xformers/benchmarks/LRA/run_with_submitit.py,sha256=M8YM_dfcC5roD6X0KtWvin0WRAA_VKJOn95FweoTGr8,4765
xformers/benchmarks/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/__pycache__/__init__.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_attn_decoding.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_blocksparse_transformers.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_causal_blocksparse.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_core.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_indexing.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_mem_eff_attention.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_mem_eff_attn_decoder.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_mlp.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_multi_head_dispatch.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_nystrom_utils.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_revnet.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_sddmm.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_swiglu.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_transformer.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_triton_blocksparse.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_triton_dropout.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_triton_fused_linear.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_triton_layernorm.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_triton_softmax.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_triton_stride_sum.cpython-311.pyc,,
xformers/benchmarks/__pycache__/utils.cpython-311.pyc,,
xformers/benchmarks/benchmark_attn_decoding.py,sha256=UZouepuUcj8lthHOT0I6YO6EeoXMSx9Vn1f3ijiRHrY,4361
xformers/benchmarks/benchmark_blocksparse_transformers.py,sha256=3_U4-t0QY68Ze_S1tdbp7p6Cd0za0Bv43IQufeqb1pU,38549
xformers/benchmarks/benchmark_causal_blocksparse.py,sha256=4prfZmn2pNBogtYYChsCpXMsf__qoezgRVYpjp-M6cc,5022
xformers/benchmarks/benchmark_core.py,sha256=usH2UjqWker63BUmTswfHZC65g3t0PwUk22eFx5jfJM,8785
xformers/benchmarks/benchmark_indexing.py,sha256=FOijbbwr8lCynutt5TB-XizFSJip-kw-H-cRgyVcaYU,5481
xformers/benchmarks/benchmark_mem_eff_attention.py,sha256=rt4XuAyynP3JWCenesZRXB6Tns2uQ8lDgmtSJFRKXns,9282
xformers/benchmarks/benchmark_mem_eff_attn_decoder.py,sha256=bqzyxcmUnv_0z_rWaPLIA75pWuC2Hgu4Nd_XbWLmXAE,5289
xformers/benchmarks/benchmark_mlp.py,sha256=fNldaniGF0fOf21A3-LxqopCknFSrM-M9jEaHygNaG0,4326
xformers/benchmarks/benchmark_multi_head_dispatch.py,sha256=Lj9FS3uroSASUDsqC_Losws9AcfK1PW4wR715O9V98A,3470
xformers/benchmarks/benchmark_nystrom_utils.py,sha256=s_O1J4hioJF0VrLjJJK-R2y3RNt_dbHzIeH-mCJ2kno,3273
xformers/benchmarks/benchmark_revnet.py,sha256=UU1pht9zhGZaS6V0yjyiT518VfXpt9pxRE6DdA9yvV0,2754
xformers/benchmarks/benchmark_sddmm.py,sha256=ZInDg18dpQu4UdUOt3hba-qESvZMlyQBy5d_1CfpHBo,3775
xformers/benchmarks/benchmark_swiglu.py,sha256=kd_A6cLw3-kEeN0sKsJXhXu_hOAqQcDObbPcmirGpCY,4341
xformers/benchmarks/benchmark_transformer.py,sha256=KzM0-TpcXPt-DDpTH2Ts7-DA1K75RCqQaRL0PEQOl58,4598
xformers/benchmarks/benchmark_triton_blocksparse.py,sha256=aLOqqLOcPz7JQnsxNmypMnsxti3o2zNNOK55eZHf0EQ,5249
xformers/benchmarks/benchmark_triton_dropout.py,sha256=3x-OA_0v3oR8_Uas0O_f75WLbAkTsteil7rzq-qG2co,3592
xformers/benchmarks/benchmark_triton_fused_linear.py,sha256=msJ7inLWMtPNOmFZHGCUCWXI8M4sWO1qSyD4JY9mJ8k,5799
xformers/benchmarks/benchmark_triton_layernorm.py,sha256=bMPpAnU8eQB3BlgObMp5LJo1vfm9KYcCuV_skoa1VH8,2775
xformers/benchmarks/benchmark_triton_softmax.py,sha256=Ksdd7E0TtHjqOYKYQf7SYNNN82UT-KfDMmzF4019NP4,2273
xformers/benchmarks/benchmark_triton_stride_sum.py,sha256=lieAAGN-oPYrX-WBVEqUTf8ypjg14i-xESEDGm6APc4,1894
xformers/benchmarks/utils.py,sha256=C7hc2b327Oy0PTXjfSKNbjnwN1j09u5avtKVMXPpnnk,24407
xformers/checkpoint.py,sha256=aDYkbaS6w8Gt3FLOUTGctIHmDCHli7U8m5OGQ5_NwTM,9765
xformers/components/__init__.py,sha256=SzC-6LpTEhg6lzGfKYOeC6KLOVRYlw_2E9NBEd8ovck,3250
xformers/components/__pycache__/__init__.cpython-311.pyc,,
xformers/components/__pycache__/activations.cpython-311.pyc,,
xformers/components/__pycache__/input_projection.cpython-311.pyc,,
xformers/components/__pycache__/multi_head_dispatch.cpython-311.pyc,,
xformers/components/__pycache__/patch_embedding.cpython-311.pyc,,
xformers/components/__pycache__/residual.cpython-311.pyc,,
xformers/components/__pycache__/reversible.cpython-311.pyc,,
xformers/components/__pycache__/simplicial_embedding.cpython-311.pyc,,
xformers/components/activations.py,sha256=D49k5HyEBWOTmGpTjCehi66tooAHWWcKW8vRyBh7dmY,1908
xformers/components/attention/__init__.py,sha256=I5Z5O5EiaE-Qjv7TaIeTd9wqZiViwfarOAu2nCQNrpM,4116
xformers/components/attention/__pycache__/__init__.cpython-311.pyc,,
xformers/components/attention/__pycache__/_sputnik_sparse.cpython-311.pyc,,
xformers/components/attention/__pycache__/attention_mask.cpython-311.pyc,,
xformers/components/attention/__pycache__/attention_patterns.cpython-311.pyc,,
xformers/components/attention/__pycache__/base.cpython-311.pyc,,
xformers/components/attention/__pycache__/blocksparse.cpython-311.pyc,,
xformers/components/attention/__pycache__/compositional.cpython-311.pyc,,
xformers/components/attention/__pycache__/core.cpython-311.pyc,,
xformers/components/attention/__pycache__/favor.cpython-311.pyc,,
xformers/components/attention/__pycache__/fourier_mix.cpython-311.pyc,,
xformers/components/attention/__pycache__/global_tokens.cpython-311.pyc,,
xformers/components/attention/__pycache__/lambda_layer.cpython-311.pyc,,
xformers/components/attention/__pycache__/linformer.cpython-311.pyc,,
xformers/components/attention/__pycache__/local.cpython-311.pyc,,
xformers/components/attention/__pycache__/nystrom.cpython-311.pyc,,
xformers/components/attention/__pycache__/ortho.cpython-311.pyc,,
xformers/components/attention/__pycache__/pooling.cpython-311.pyc,,
xformers/components/attention/__pycache__/random.cpython-311.pyc,,
xformers/components/attention/__pycache__/scaled_dot_product.cpython-311.pyc,,
xformers/components/attention/__pycache__/sparsity_config.cpython-311.pyc,,
xformers/components/attention/__pycache__/utils.cpython-311.pyc,,
xformers/components/attention/__pycache__/visual.cpython-311.pyc,,
xformers/components/attention/_sputnik_sparse.py,sha256=LOjNGsNeagDL6_VCOafkJLrK4exsefTdrIEejm3RYds,3285
xformers/components/attention/attention_mask.py,sha256=yidod0KphKvup4oP3Y-oKQNnWfUddwDZDV5KlzAgzJI,4728
xformers/components/attention/attention_patterns.py,sha256=2pliuyA3AfM8t2QX4YPoUopP5ryUkPfRxvHo-IT5biY,10240
xformers/components/attention/base.py,sha256=G1p2Hk1tHPK7GFJLBF3JaGkiWPeg12X95erSzVTXnRM,3302
xformers/components/attention/blocksparse.py,sha256=Do0C8aDg5IiVOW-kb31izUw-VfMkRpMHE2CmHwsNpGA,6915
xformers/components/attention/compositional.py,sha256=FcwBJp30teQZbJbXrQUHukg2OomBJQ8VfNVDlrhUwoI,13295
xformers/components/attention/core.py,sha256=VbQNr-Q5pzR8nYEjz2uxql5uIdqUm93adKqOaOXbiI0,11124
xformers/components/attention/favor.py,sha256=3ApGdLze9h_rte4vbQJDn3OM5EJBMjz_KHFJU028dKY,6364
xformers/components/attention/feature_maps/__init__.py,sha256=_RoN4rqRjxEef9YFLvPkUG68aHMi-ojOmZgr1Crsjq0,618
xformers/components/attention/feature_maps/__pycache__/__init__.cpython-311.pyc,,
xformers/components/attention/feature_maps/__pycache__/base.cpython-311.pyc,,
xformers/components/attention/feature_maps/__pycache__/softmax.cpython-311.pyc,,
xformers/components/attention/feature_maps/base.py,sha256=zGtAsRXSUQ9AXNCW3jhJjMr0R0H6neDWpLGnaDU9--U,1733
xformers/components/attention/feature_maps/softmax.py,sha256=Uwqauwv387F8dSyLNOlIH38ILIoZ2wRFSSotdgrSJys,10894
xformers/components/attention/fourier_mix.py,sha256=IfCX3prc77mJnK9eIsA6dv_lXa4R-1HQX_54eT3wH3c,1216
xformers/components/attention/global_tokens.py,sha256=FdAiJRmaXb1gHcqFXDjNqdC2oEWmcX7BwBctZ2x3rkE,4213
xformers/components/attention/lambda_layer.py,sha256=o97Ad1sKNsb68IoihW2cd7qonFyL2ujbn5NPoPqBM7M,2892
xformers/components/attention/linformer.py,sha256=McNvHVz3Q9qaQxlFZl3M4ZoQ2kpCUKpUoUTdPj5SotM,2565
xformers/components/attention/local.py,sha256=Qzue_zFts2dAm4C33Zj5gNMvTQEjGEUGOh-QgputAaA,3935
xformers/components/attention/nystrom.py,sha256=-fgz0Xqvdax61hIHcM7-OW5aC1mZBICFaIhylTKELI4,11700
xformers/components/attention/ortho.py,sha256=Z3-9eKAdcg1vxvJNV8xVypPv02csXsZkiPRcvC36K6g,12448
xformers/components/attention/pooling.py,sha256=uXFWAcuLxhW_kl5gYLEQVISJF7Sh13el4YGqflhsMnU,2572
xformers/components/attention/random.py,sha256=mcU2R0pJcjn9bwzN0f4KIk9Jf0A-GcBYlzM55aeF2X0,4257
xformers/components/attention/scaled_dot_product.py,sha256=HnhYiNOghpBWucf2tW3aYVtFlbPIdUz8izCVwotsZFI,4638
xformers/components/attention/sparsity_config.py,sha256=5I77G7bv9pIY2x9qFvcHocZGmnXcN0vdx4C3XgYz8s0,42420
xformers/components/attention/utils.py,sha256=rhtgdGUKRBHBesndiJWAttw1tpaNFxf0qbGdfcWQj4U,3911
xformers/components/attention/visual.py,sha256=4_8kXCaH_FM9i3xH6u__G9iH9fKmc2EfhQHqbgVJJ0I,3025
xformers/components/feedforward/__init__.py,sha256=GK3J7Zdzw_UQm0OcVm32pnFMClGfeQTzIJkEZ3nbDpE,2643
xformers/components/feedforward/__pycache__/__init__.cpython-311.pyc,,
xformers/components/feedforward/__pycache__/base.cpython-311.pyc,,
xformers/components/feedforward/__pycache__/conv_mlp.cpython-311.pyc,,
xformers/components/feedforward/__pycache__/fused_mlp.cpython-311.pyc,,
xformers/components/feedforward/__pycache__/mixture_of_experts.cpython-311.pyc,,
xformers/components/feedforward/__pycache__/mlp.cpython-311.pyc,,
xformers/components/feedforward/base.py,sha256=Mbxuue6ryioJS65yMkI-bQrym3IMbDMUH3OIIVz515o,1551
xformers/components/feedforward/conv_mlp.py,sha256=-nSpnPKiYlFXcRf5bl_FUqCQEafMefB1Epv5l1dIrsU,3108
xformers/components/feedforward/fused_mlp.py,sha256=KG7271QcymcpWewcJ2R_u48qunNpmkyL0BlsuSi3rPs,2658
xformers/components/feedforward/mixture_of_experts.py,sha256=TUsjueVHDyqLvjMIMr9DFsPVrVuS6U2-PUSt2eBSpuI,5496
xformers/components/feedforward/mlp.py,sha256=L54SVLT7gcvIjGc0SGMY7NqGjZnZN9p0cQKARwMraAU,1355
xformers/components/input_projection.py,sha256=pSwLDaionOZd3GX8rjcR_MxIf16XOBR4iZ-Sgtrfc2M,3123
xformers/components/multi_head_dispatch.py,sha256=3B6Wnexu6z0RcijTThoew_m0bV5zlh4T0yoNKUGJ5U0,10438
xformers/components/patch_embedding.py,sha256=9sdBQFWvGwa9ZmfRZofBZCj6zt9qy85yfLA3GiIl9wE,2348
xformers/components/positional_embedding/__init__.py,sha256=sdK4_V_lrvEZ-BcaBcJTachrdzuBcg_7lKdZkbqtMtI,2842
xformers/components/positional_embedding/__pycache__/__init__.cpython-311.pyc,,
xformers/components/positional_embedding/__pycache__/base.cpython-311.pyc,,
xformers/components/positional_embedding/__pycache__/param.cpython-311.pyc,,
xformers/components/positional_embedding/__pycache__/rotary.cpython-311.pyc,,
xformers/components/positional_embedding/__pycache__/sine.cpython-311.pyc,,
xformers/components/positional_embedding/__pycache__/vocab.cpython-311.pyc,,
xformers/components/positional_embedding/base.py,sha256=ZvDSQtEr3XRQAHYdXQD2au1kgBAIbR9CsLSUfGijn20,1007
xformers/components/positional_embedding/param.py,sha256=uPExzq7PbwiDUBNV-l5Unb9VQrvUcd2AurV6qh3IOf8,1598
xformers/components/positional_embedding/rotary.py,sha256=uPk88A0Z1tTKLojqfwfy9TRX7BANfoyS6Cz2ocNyBxo,3365
xformers/components/positional_embedding/sine.py,sha256=I_DUfRiDJUdAjdYEkQW_7yzUcAXArlZZFR3KaeoM4W8,1411
xformers/components/positional_embedding/vocab.py,sha256=bxO6WA12xTf5AJYkJAO5QcZJUHndp0vyjpLlCQKekAg,1833
xformers/components/residual.py,sha256=R5Mfkm0aAsmIThLWc8dWJB2HIzVuUTUjz-iW8htCH6Y,6331
xformers/components/reversible.py,sha256=0HXdVqIfjr1wg1UZSw_9K30Gf18abrgXnZPX1edGFXA,5417
xformers/components/simplicial_embedding.py,sha256=P0ft4QvJ8lOe7TAKZjAtwQ4RiStzCWR2Zovg33e62mU,2396
xformers/cpp_lib.json,sha256=KTUt34rhFVeoH8mbfWuxDAU2oXpsf47td_VvKi3pytY,306
xformers/factory/__init__.py,sha256=CUqWDKGiYqZkz9eWSRtMvlwp2SkyXJXkpdAVYO9uN-o,628
xformers/factory/__pycache__/__init__.cpython-311.pyc,,
xformers/factory/__pycache__/block_configs.cpython-311.pyc,,
xformers/factory/__pycache__/block_factory.cpython-311.pyc,,
xformers/factory/__pycache__/hydra_helper.cpython-311.pyc,,
xformers/factory/__pycache__/model_factory.cpython-311.pyc,,
xformers/factory/__pycache__/weight_init.cpython-311.pyc,,
xformers/factory/block_configs.py,sha256=N5ZNDhK4dfID8ZoToR1BBJuyGmTlf4gI9zKO00Px01Y,8412
xformers/factory/block_factory.py,sha256=DCRjODE0MUQTlwp1D6h7lDDwf1YQjcLQSHM59ZZ2kHs,13318
xformers/factory/hydra_helper.py,sha256=wOevew190gZ_OmQR68CRWlLj4rFxKbPHRG9GaCMCESM,1348
xformers/factory/model_factory.py,sha256=Eu37gkEOHaMgB1G2QdjWvlUzDIVdZnHU55zk80Bs-wQ,12254
xformers/factory/weight_init.py,sha256=e6O9Wh7TF4EwOrP9GplQQ05REPFi7uozdjXvMAiTIOg,10281
xformers/helpers/__init__.py,sha256=ZiPDfLiNbASRD4kqsncasNa6VwseDeui4XpjbrgcOmc,270
xformers/helpers/__pycache__/__init__.cpython-311.pyc,,
xformers/helpers/__pycache__/hierarchical_configs.cpython-311.pyc,,
xformers/helpers/__pycache__/test_utils.cpython-311.pyc,,
xformers/helpers/__pycache__/timm_sparse_attention.cpython-311.pyc,,
xformers/helpers/hierarchical_configs.py,sha256=57ZK0dMMEAB4gOYyNMrA9sh-jDRNYyzYZhC2aMfi-6g,4048
xformers/helpers/test_utils.py,sha256=MBiBlBN4NFEZdjBnOVKYsK-3p001tDv8fobWF92ypro,832
xformers/helpers/timm_sparse_attention.py,sha256=ElroZACvcnBD6pfQ4iEfAFvkZvPg6kAtzJZNToBypZo,1583
xformers/info.py,sha256=sptYY-AGzbTLt9SVJej8L7dsuBG57nezZrj4_QAnFso,2513
xformers/ops/__init__.py,sha256=5qbUrZV9etTRmvFxZDBIVtImWp2i4MfZArPN8vBvQpw,2670
xformers/ops/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/__pycache__/common.cpython-311.pyc,,
xformers/ops/__pycache__/indexing.cpython-311.pyc,,
xformers/ops/__pycache__/rmsnorm.cpython-311.pyc,,
xformers/ops/__pycache__/rope_padded.cpython-311.pyc,,
xformers/ops/__pycache__/swiglu_op.cpython-311.pyc,,
xformers/ops/__pycache__/unbind.cpython-311.pyc,,
xformers/ops/_triton/__init__.py,sha256=qYpTCiWro4PUKxT_LYfaCxIfvx5qyan3IbbbSN0hbw0,594
xformers/ops/_triton/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/k_index_select_cat.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/k_scaled_index_add.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/rmsnorm_kernels.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/rope_padded_kernels.cpython-311.pyc,,
xformers/ops/_triton/k_index_select_cat.py,sha256=_XVj_ytE-qcG7xUBAXfacJWFG9H9hQ609ar6TTXEo2Y,6378
xformers/ops/_triton/k_scaled_index_add.py,sha256=-J3qUjQsWL3G4d8h5D-Xul9ljyOfBMvEtCzw12ceq0Y,13167
xformers/ops/_triton/rmsnorm_kernels.py,sha256=LIPt5DqMDW0-6offX8KHnhLHTu2JTnDXdeNBq2TvFf8,5087
xformers/ops/_triton/rope_padded_kernels.py,sha256=GH2YNY8IIcXLWdPUo8af8Ts_xZ0QekPenWlqMAEwNpE,6082
xformers/ops/common.py,sha256=pXBUo5vdfC3eEeqtHjwraYQ0j4NbVAyUVgRolL9soxE,3885
xformers/ops/fmha/__init__.py,sha256=yEuS8U5-JTNGh4-lYXRcHD6zlqYPpDIMQE1eGFieBu4,15480
xformers/ops/fmha/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/attn_bias.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/common.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/cutlass.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/decoder.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/dispatch.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/flash.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/small_k.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/triton.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/triton_splitk.cpython-311.pyc,,
xformers/ops/fmha/attn_bias.py,sha256=9jNh8iCBpUnFygJ-CHxyPFjrdyLT6_QAh__c_AiNfok,28938
xformers/ops/fmha/common.py,sha256=k_CAatCw2s5v95ak7VvxmFCnVMgE0C7KEekweZWIM_Q,20293
xformers/ops/fmha/cutlass.py,sha256=Mr7groAGrD-81Ol9b8lX6yF75mc_6lAn_K_p70EUvsY,17944
xformers/ops/fmha/decoder.py,sha256=bV2LIvFULdKQpDYb3oW8OykKuUCLVbxNiSYZa5dis3E,3638
xformers/ops/fmha/dispatch.py,sha256=XpOzMzhhrmB8dBRoIj84NK__tcNYKNQmfUmzkR3Mee4,5227
xformers/ops/fmha/flash.py,sha256=tigy1PIzfWKGnpo5IBKVJuPIfnRo19K81RhNKKdgXzc,20476
xformers/ops/fmha/small_k.py,sha256=2Tw9i7lsUFbdNxkIxmaCtUQDteM2_CaafrZZhkFB34s,6523
xformers/ops/fmha/triton.py,sha256=nIZSuF5WeDBMOd92SQ3KTgZYBDS-amkoCdb4jw73ZEM,7753
xformers/ops/fmha/triton_splitk.py,sha256=dcQDBXUmpdoLrqYLD_ymJ0W9y545bT206dsNABUofAI,27608
xformers/ops/indexing.py,sha256=PNItxgTsir-KlMyKSWtgEoGwOa7VDLx5JlkJXGKvfj8,7164
xformers/ops/rmsnorm.py,sha256=5t_BN6OXyz7FpnRQn_8UvhpA8f5QjCnyyelsY5s2Crg,3471
xformers/ops/rope_padded.py,sha256=5hXfGvJTaMD-41QTeW76SdYxnudjlsFDpxAyos29cws,8642
xformers/ops/swiglu_op.py,sha256=RpEKv684DgYDVoHNlbCaLWotpj__nbhmdiT8Y8MHOTc,15870
xformers/ops/unbind.py,sha256=HW6z8fWkhKRRYC0JSsrCvsjW1o8jP8o1512U_2tks7g,3681
xformers/profiler/__init__.py,sha256=hOObg7U_RE-uZj3WdE926s_tIlTSqdKbWR0-jaJPD-c,520
xformers/profiler/__pycache__/__init__.cpython-311.pyc,,
xformers/profiler/__pycache__/api.cpython-311.pyc,,
xformers/profiler/__pycache__/device_limits.cpython-311.pyc,,
xformers/profiler/__pycache__/profiler.cpython-311.pyc,,
xformers/profiler/__pycache__/slow_ops_profiler.cpython-311.pyc,,
xformers/profiler/api.py,sha256=T5OjijgKFHcg7gj8js14kjsiUW3STyt8zmTARnRUyjE,2920
xformers/profiler/device_limits.py,sha256=hlhCdR5Zw_vc8YLpPLmP_pWtZ_o5yc4zo_7JL0yFn3M,3690
xformers/profiler/profiler.py,sha256=DMJoTvcanArznKTDl8DZy8GKQlmiqMxhJHzwwBd2fjc,12469
xformers/profiler/slow_ops_profiler.py,sha256=Oqlmh0oWNPLJfpPvC5r5HRU1yRcWz8l7HojyhZSNZNA,16891
xformers/sparse/__init__.py,sha256=dgI-6hEZfhbzKszzAeGSux-uumEG2PnKrePQC0uJcks,324
xformers/sparse/__pycache__/__init__.cpython-311.pyc,,
xformers/sparse/__pycache__/_csr_ops.cpython-311.pyc,,
xformers/sparse/__pycache__/blocksparse_tensor.cpython-311.pyc,,
xformers/sparse/__pycache__/csr_tensor.cpython-311.pyc,,
xformers/sparse/__pycache__/utils.cpython-311.pyc,,
xformers/sparse/_csr_ops.py,sha256=cJyyY_pCxEnpUwfvhyJWKesEU2azf15gh-nJeTheSLU,5039
xformers/sparse/blocksparse_tensor.py,sha256=V50OiMLIx6Ue6NG59LrKEzkkutgl4U8P27lKzg9wOko,11914
xformers/sparse/csr_tensor.py,sha256=_zjaU3HW7b-KSx9w1QYntjv0WYVdpaF9djbYV1pEi5M,14714
xformers/sparse/utils.py,sha256=WL9FQ6UWBiHWcbmu3XcnGeOXb_MKr1gDP32ypxvXYGk,4397
xformers/test.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/triton/__init__.py,sha256=IoVYlaavADc-bNappoT5v1cfeSvtotLQaVsqDmMwp3A,830
xformers/triton/__pycache__/__init__.cpython-311.pyc,,
xformers/triton/__pycache__/dropout.cpython-311.pyc,,
xformers/triton/__pycache__/fused_linear_layer.cpython-311.pyc,,
xformers/triton/__pycache__/k_activations.cpython-311.pyc,,
xformers/triton/__pycache__/k_dropout.cpython-311.pyc,,
xformers/triton/__pycache__/k_fused_matmul_bw.cpython-311.pyc,,
xformers/triton/__pycache__/k_fused_matmul_fw.cpython-311.pyc,,
xformers/triton/__pycache__/k_layer_norm.cpython-311.pyc,,
xformers/triton/__pycache__/k_softmax.cpython-311.pyc,,
xformers/triton/__pycache__/k_sum.cpython-311.pyc,,
xformers/triton/__pycache__/layer_norm.cpython-311.pyc,,
xformers/triton/__pycache__/softmax.cpython-311.pyc,,
xformers/triton/__pycache__/sum_strided.cpython-311.pyc,,
xformers/triton/__pycache__/utils.cpython-311.pyc,,
xformers/triton/__pycache__/vararg_kernel.cpython-311.pyc,,
xformers/triton/dropout.py,sha256=R9fyBHEn1uYT8dcD7ugxnFrXKgoKRjrNl0ZEuy_ki3g,7858
xformers/triton/fused_linear_layer.py,sha256=_4O_LQElF3G2SFOQHo0SnB_7bWLjeMZmycqSKQgr3yI,4050
xformers/triton/k_activations.py,sha256=-bFmuWm6ilKVl37dDzE71Dj7KTj2_KFNrawk1JZs4pg,3704
xformers/triton/k_dropout.py,sha256=qDX59UjGbVqxxfZD2Pkqhx1rWVJuz7ueCN8NkABygzE,6152
xformers/triton/k_fused_matmul_bw.py,sha256=WosjW84uXH_VsJGDqKuAb1VUD265Qz_0ULK4qniSDVg,5318
xformers/triton/k_fused_matmul_fw.py,sha256=UMjmj0A4xKsSmzF14SoGdnwLE_FKnNstoqDA_6kah18,8679
xformers/triton/k_layer_norm.py,sha256=gjgkwBS7nkbb8karKSLt6uLutkMCO-X5GfdnVM1WUeY,5238
xformers/triton/k_softmax.py,sha256=Lj0-FhWlHFCq_XQ5H6_gK6Przw9Q9Y_hifoMeilEu0Y,5243
xformers/triton/k_sum.py,sha256=DBcJ6pQGCzCzBRfz49VX33_iaBrYa_a0dcn8Keai-EI,1625
xformers/triton/layer_norm.py,sha256=YlgqJ6EOgXpi63EyEqbQH_ECYHLQo0AvLNz89vIOSQ8,7895
xformers/triton/softmax.py,sha256=cOxXTEsv6NaqtrzbUm4MfrXPTiJ86OZM93uRdSJE2aQ,6580
xformers/triton/sum_strided.py,sha256=BkfsuLyuhdFOU7itt0aFcycUY01jINxCm3nFokmEnho,1517
xformers/triton/utils.py,sha256=nTViQIcxPmEofWVUx-5TPC9gCJm3EPMy5Qf1YcoQzy4,1227
xformers/triton/vararg_kernel.py,sha256=KztVNfo9LKT0aXMJ3ioDymlqdeT5qKDGLLirZ6DBSz0,6043
xformers/utils.py,sha256=_7fPEX_TLYh1SbnR1_VFmXslO_DAhI1ZngUuFn6Kd_o,2827
xformers/version.py,sha256=F52-3Xi4sBAjMa6ohw8l4nI3FY87htFqX8M26zI1Kxs,44
